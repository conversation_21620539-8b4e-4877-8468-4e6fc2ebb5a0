package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.hazelpay.merchant.tap2pay.utils.clearFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.getFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import com.hazelpay.merchant.tap2pay.utils.parseJsonToMap
import com.hazelpay.merchant.tap2pay.utils.sanitizeJsonString
import com.hazelpay.merchant.tap2pay.utils.sendReceiptEmail
import kotlinx.coroutines.launch
import org.json.JSONException

class AddEmailScreen : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_add_email_screen)
        enforceOrientation(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        val emailEditText = findViewById<EditText>(R.id.email_edittext)
        val confirmButton = findViewById<LinearLayout>(R.id.ll_confirm_button)

        confirmButton.setOnClickListener {
            val email = emailEditText.text.toString().trim()
            val tipPercentage = getFromSharedPreferences("tip_percentage").toString()
            val tipAmount = getFromSharedPreferences("tip_amount").toString()

            if (email.isEmpty()) {
                Toast.makeText(this, getString(R.string.enter_email_address), Toast.LENGTH_SHORT)
                    .show()
            } else {
                showLoader()

                val receiptData = getFromSharedPreferences("trn_receipt")
                if (receiptData != null) {
                    try {
                        val sanitizedJson = sanitizeJsonString(receiptData)
                        val dataMap = parseJsonToMap(sanitizedJson)
                        val parentId = getUserParentData()?.id!!

                        val savedVAT = getFromSharedPreferences("VAT")
                        val vat = savedVAT?.trim()?.takeIf { it.isNotEmpty() } ?: "0%"

                        lifecycleScope.launch {
                            val success =
                                sendReceiptEmail(
                                    this@AddEmailScreen,
                                    email,
                                    dataMap,
                                    vat,
                                    tipPercentage,
                                    tipAmount,
                                    parentId
                                )

                            if (success) {
                                hideLoader()
                                Toast.makeText(
                                    this@AddEmailScreen,
                                    "Email Sent Successfully",
                                    Toast.LENGTH_LONG
                                ).show()

                                clearFromSharedPreferences("trn_receipt")
                                clearFromSharedPreferences("tip_percentage")
                                clearFromSharedPreferences("tip_amount");

                                val intent =
                                    Intent(this@AddEmailScreen, HomeScreen::class.java).apply {
                                        addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                                    }
                                startActivity(intent)
                                finishAffinity()
                            } else {
                                hideLoader()
                                Toast.makeText(
                                    this@AddEmailScreen,
                                    "An Unexpected Error Occurred while sending email receipt",
                                    Toast.LENGTH_LONG
                                ).show()
                                clearFromSharedPreferences("trn_receipt")
                                val intent =
                                    Intent(this@AddEmailScreen, HomeScreen::class.java).apply {
                                        addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                                    }
                                startActivity(intent)
                                finishAffinity()
                            }
                        }
                    } catch (e: JSONException) {
                        Log.e("AddEmailScreen", "JSON Parsing Error: ${e.localizedMessage}")
                    }
                }

            }
        }

        findViewById<ImageButton>(R.id.ib_backbutton).setOnClickListener {
            val intent = Intent(this, PaymentReceiptScreen::class.java)
            startActivity(intent)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun showLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.VISIBLE
    }

    private fun hideLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.GONE
    }
}
