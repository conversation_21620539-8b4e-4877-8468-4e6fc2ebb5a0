package com.hazelpay.merchant.tap2pay.model

data class ReceiptData(
    // Merchant Information
    val companyName: String,
    val companyId: String,
    val address: String,
    
    // Transaction Information
    val transactionId: String,
    val amount: String,
    val currency: String,
    val dateTime: String,
    val cardMask: String,
    val paymentMethod: String,
    val rrn: String,
    val authCode: String,
    val aid: String,
    val tvr: String?,
    
    // VAT and Tip Information
    val vatPercentage: String?,
    val vatAmount: String?,
    val tipPercentage: String?,
    val tipAmount: String?,
    val subtotal: String,
    val total: String,
    
    // Terminal Information
    val terminalId: String,
    val merchantId: String,
    
    // Status
    val status: String
)
