package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.WindowManager
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import com.hazelpay.merchant.tap2pay.utils.clearFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation

class TransferSuccessfulScreen : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_transfer_successful_screen)
        enforceOrientation(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)


        findViewById<LinearLayout>(R.id.ll_share_via_email).setOnClickListener {
            val intent = Intent(this, AddEmailScreen::class.java)
            startActivity(intent)
        }

        findViewById<LinearLayout>(R.id.ll_view_receipt).setOnClickListener {
            val intent = Intent(this, PaymentReceiptScreen::class.java)
            startActivity(intent)
        }

        findViewById<ImageView>(R.id.iv_back_arrow).setOnClickListener {
            clearFromSharedPreferences("trn_receipt")
            val intent = Intent(this, HomeScreen::class.java)
            startActivity(intent)
            finish()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }
}
