package com.hazelpay.merchant.tap2pay

import android.Manifest
import android.app.AlertDialog
import android.content.ContentValues
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.hazelpay.merchant.tap2pay.utils.clearFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.convertToUserTimeZone
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.getFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.parseJsonToMap
import com.hazelpay.merchant.tap2pay.utils.sanitizeJsonString
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import com.hazelpay.merchant.tap2pay.utils.getUserTerminalData
import com.hazelpay.merchant.tap2pay.utils.PrinterUtils
import com.hazelpay.merchant.tap2pay.model.ReceiptData
import org.json.JSONException
import java.io.File
import java.io.FileOutputStream
import kotlin.text.get

class PaymentReceiptScreen : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_payment_reciept_screen)
        enforceOrientation(this)

        // Keep screen awake
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        val receiptData = getFromSharedPreferences("trn_receipt")
        val receiptVAT = findViewById<LinearLayout>(R.id.ll_vat_receipt_screen)
        val savedVAT = getFromSharedPreferences("VAT")
        val tipEnabled = getFromSharedPreferences("TIP")?.toBoolean() ?: false
        val tipPercentage = getFromSharedPreferences("tip_percentage")
        val tipAmount = getFromSharedPreferences("tip_amount")

        if (receiptData != null) {
            try {
                val sanitizedJson = sanitizeJsonString(receiptData)
                val dataMap = parseJsonToMap(sanitizedJson)

                findViewById<TextView>(R.id.receipt_value).text = dataMap["trxid"]
                findViewById<TextView>(R.id.amount_value).text = dataMap["cur_code"] + " " + dataMap["amt"]
                findViewById<TextView>(R.id.merchant_name_value).text = dataMap["pmt_dest"]
                findViewById<TextView>(R.id.payment_method_value).text = dataMap["applbl"]

                val localDateTime = if (dataMap["date_time"] != null) convertToUserTimeZone(dataMap["date_time"]!!) else null

                findViewById<TextView>(R.id.date_value).text = localDateTime ?: dataMap["date_time"]
                findViewById<TextView>(R.id.card_detail_value).text = dataMap["card_mask"]
                findViewById<TextView>(R.id.transaction_type_value).text = dataMap["pmt_name"]
                findViewById<TextView>(R.id.tvr_value).text = dataMap["tvr"]
                findViewById<TextView>(R.id.rrn_value).text = dataMap["rrn"]
                findViewById<TextView>(R.id.auth_code_value).text = dataMap["auth_code"]
                findViewById<TextView>(R.id.aid_value).text = dataMap["aid"]

                if (!savedVAT.isNullOrEmpty() && savedVAT != getString(R.string.vat_off)) {
                    receiptVAT.visibility = View.VISIBLE
                    findViewById<TextView>(R.id.tv_vat_value).text = savedVAT

                    dataMap["amt"]?.let { amountString ->
                        try {
                            val amount = amountString.toDouble()
                            val vatPercentage = savedVAT.replace("%", "").toDouble()
                            val vatAmount = (amount * (vatPercentage / 100.0) * 100).toLong().toDouble() / 100

                            val formattedVat = String.format("%.2f", vatAmount)
                            findViewById<LinearLayout>(R.id.ll_vat_receipt_screen_value).visibility = View.VISIBLE;
                            findViewById<TextView>(R.id.tv_vat_calculated).text = dataMap["cur_code"] + " " + formattedVat;
                        } catch (_: NumberFormatException) {
                        }
                    }
                }

                if (tipEnabled){
                    findViewById<LinearLayout>(R.id.ll_tip_receipt_screen).visibility = View.VISIBLE;
                    findViewById<LinearLayout>(R.id.ll_tip_receipt_screen_value).visibility = View.VISIBLE;
                    findViewById<TextView>(R.id.tv_tip_value).text = tipPercentage
                    findViewById<TextView>(R.id.tv_tip_calculated).text = dataMap["cur_code"] + " " + tipAmount
                }

            } catch (e: JSONException) {
                Log.e("PaymentReceiptScreen", "JSON Parsing Error: ${e.localizedMessage}")
            }
        }

        findViewById<LinearLayout>(R.id.ll_print_receipt).setOnClickListener {
            printReceipt()
        }

        findViewById<LinearLayout>(R.id.ll_share_via_email).setOnClickListener {
            val intent = Intent(this, AddEmailScreen::class.java)
            startActivity(intent)
        }

        findViewById<ImageView>(R.id.iv_back_arrow).setOnClickListener {
            clearFromSharedPreferences("trn_receipt")
            clearFromSharedPreferences("tip_percentage")
            clearFromSharedPreferences("tip_amount");

            val intent = Intent(this, HomeScreen::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            startActivity(intent)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun printReceipt() {
        try {
            val receiptData = getFromSharedPreferences("trn_receipt")
            if (receiptData == null) {
                Toast.makeText(this, "No receipt data available", Toast.LENGTH_SHORT).show()
                return
            }

            val sanitizedJson = sanitizeJsonString(receiptData)
            val dataMap = parseJsonToMap(sanitizedJson)

            // Get merchant data
            val parentData = getUserParentData()
            val terminalData = getUserTerminalData()

            if (parentData == null) {
                Toast.makeText(this, "Merchant data not available", Toast.LENGTH_SHORT).show()
                return
            }

            // Get VAT and tip information
            val savedVAT = getFromSharedPreferences("VAT")
            val tipEnabled = getFromSharedPreferences("TIP")?.toBoolean() ?: false
            val tipPercentage = getFromSharedPreferences("tip_percentage")
            val tipAmount = getFromSharedPreferences("tip_amount")

            // Calculate subtotal and total
            val amount = dataMap["amt"]?.toDoubleOrNull() ?: 0.0
            var subtotal = amount
            var vatAmount = 0.0

            // Calculate VAT amount if available
            if (!savedVAT.isNullOrEmpty() && savedVAT != getString(R.string.vat_off)) {
                val vatPercentage = savedVAT.replace("%", "").toDoubleOrNull() ?: 0.0
                vatAmount = (amount * (vatPercentage / 100.0) * 100).toLong().toDouble() / 100
                subtotal = amount - vatAmount
            }

            // Create receipt data
            val receiptDataObj = ReceiptData(
                companyName = parentData.company ?: parentData.merchantName ?: "Unknown Company",
                companyId = parentData.companyId ?: parentData.id,
                address = parentData.address ?: "Address not available",
                transactionId = dataMap["trxid"] ?: "",
                amount = dataMap["amt"] ?: "",
                currency = dataMap["cur_code"] ?: "",
                dateTime = convertToUserTimeZone(dataMap["date_time"] ?: "") ?: dataMap["date_time"] ?: "",
                cardMask = dataMap["card_mask"] ?: "",
                paymentMethod = dataMap["applbl"] ?: "",
                rrn = dataMap["rrn"] ?: "",
                authCode = dataMap["auth_code"] ?: "",
                aid = dataMap["aid"] ?: "",
                tvr = dataMap["tvr"],
                vatPercentage = if (!savedVAT.isNullOrEmpty() && savedVAT != getString(R.string.vat_off)) savedVAT else null,
                vatAmount = if (vatAmount > 0) String.format("%.2f", vatAmount) else null,
                tipPercentage = if (tipEnabled) tipPercentage else null,
                tipAmount = if (tipEnabled) tipAmount else null,
                subtotal = String.format("%.2f", subtotal),
                total = dataMap["amt"] ?: "",
                terminalId = terminalData?.id ?: "Unknown",
                merchantId = parentData.id,
                status = "Approved"
            )

            // Print the receipt
            PrinterUtils.printReceipt(
                context = this,
                receiptData = receiptDataObj
            ) { success, message ->
                runOnUiThread {
                    if (success) {
                        Toast.makeText(this, "Print preview opened successfully", Toast.LENGTH_LONG).show()
                    } else {
                        Toast.makeText(this, message ?: "Failed to open print preview", Toast.LENGTH_LONG).show()
                    }
                }
            }

        } catch (e: Exception) {
            Log.e("PaymentReceiptScreen", "Error printing receipt: ${e.localizedMessage}", e)
            Toast.makeText(this, "Error printing receipt: ${e.localizedMessage}", Toast.LENGTH_LONG).show()
        }
    }
}
