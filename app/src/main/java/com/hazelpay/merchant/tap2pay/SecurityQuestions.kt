package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageButton
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation

class SecurityQuestions : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_security_questions)
        enforceOrientation(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        val securityAnswer: EditText = findViewById(R.id.security_answer)
        val saveButton: ImageButton = findViewById(R.id.save_button)

        saveButton.setOnClickListener {
            val answer = securityAnswer.text.toString()

            if (answer.isEmpty()) {

                Toast.makeText(this, getString(R.string.please_provide_answer), Toast.LENGTH_SHORT).show()
            } else {
                val intent = Intent(this, PinCode::class.java)
                startActivity(intent)
                finish()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }
}
