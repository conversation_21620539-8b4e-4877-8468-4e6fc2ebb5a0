package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation

class ChangePinScreen : AppCompatActivity() {
    private var pinCode: String = ""
    private lateinit var pinIndicators: List<View>
    private lateinit var saveButton: ImageButton

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_change_pin_screen)
        enforceOrientation(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)


        val button0: ImageButton = findViewById(R.id.button0)
        val button1: ImageButton = findViewById(R.id.button1)
        val button2: ImageButton = findViewById(R.id.button2)
        val button3: ImageButton = findViewById(R.id.button3)
        val button4: ImageButton = findViewById(R.id.button4)
        val button5: ImageButton = findViewById(R.id.button5)
        val button6: ImageButton = findViewById(R.id.button6)
        val button7: ImageButton = findViewById(R.id.button7)
        val button8: ImageButton = findViewById(R.id.button8)
        val button9: ImageButton = findViewById(R.id.button9)
        val deleteButton: ImageButton = findViewById(R.id.delete_button)
        saveButton = findViewById(R.id.verify_button)

        // Initialize saveButton as disabled
        saveButton.isEnabled = false

        pinIndicators = listOf(
            findViewById(R.id.pin_indicator_1),
            findViewById(R.id.pin_indicator_2),
            findViewById(R.id.pin_indicator_3),
            findViewById(R.id.pin_indicator_4),
            findViewById(R.id.pin_indicator_5)
        )

        fun updatePinIndicators() {
            for (i in pinIndicators.indices) {
                pinIndicators[i].setBackgroundResource(
                    if (i < pinCode.length) R.drawable.filled_circle else R.drawable.empty_circle
                )
            }

            // Enable saveButton only when the PIN code length is 5
            saveButton.isEnabled = pinCode.length == 5
        }

        fun addToPin(digit: String) {
            if (pinCode.length < 5) {
                pinCode += digit
                updatePinIndicators()
            }
        }

        button0.setOnClickListener { addToPin("0") }
        button1.setOnClickListener { addToPin("1") }
        button2.setOnClickListener { addToPin("2") }
        button3.setOnClickListener { addToPin("3") }
        button4.setOnClickListener { addToPin("4") }
        button5.setOnClickListener { addToPin("5") }
        button6.setOnClickListener { addToPin("6") }
        button7.setOnClickListener { addToPin("7") }
        button8.setOnClickListener { addToPin("8") }
        button9.setOnClickListener { addToPin("9") }

        deleteButton.setOnClickListener {
            if (pinCode.isNotEmpty()) {
                pinCode = pinCode.dropLast(1)
                updatePinIndicators()
            }
        }

        saveButton.setOnClickListener {
            if (pinCode.length == 5) {
                Toast.makeText(this, getString(R.string.pin_changed_success), Toast.LENGTH_SHORT).show()
                val intent = Intent(this, HomeScreen::class.java)
                startActivity(intent)
                finish()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }
}
