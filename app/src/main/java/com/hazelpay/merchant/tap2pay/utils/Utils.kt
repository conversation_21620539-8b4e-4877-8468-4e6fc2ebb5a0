package com.hazelpay.merchant.tap2pay.utils

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Resources
import android.net.Uri
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import com.hazelpay.merchant.tap2pay.LoginScreen
import com.hazelpay.merchant.tap2pay.model.LoginData
import com.hazelpay.merchant.tap2pay.model.PaymentTerminal
import com.hazelpay.merchant.tap2pay.model.PedStatusResponse
import com.hazelpay.merchant.tap2pay.model.UserData
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.hazelpay.merchant.tap2pay.R
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.math.sqrt
import androidx.core.net.toUri
import androidx.core.content.edit
import java.text.SimpleDateFormat
import java.util.Date

fun Context.saveToSharedPreferences(key: String, value: String) {
    val sharedPreferences = getSharedPreferences("EftaaPayPrefs", Context.MODE_PRIVATE)
    sharedPreferences.edit().apply {
        putString(key, value)
        apply()
    }
}

fun Context.getFromSharedPreferences(key: String): String? {
    val sharedPreferences = getSharedPreferences("EftaaPayPrefs", Context.MODE_PRIVATE)
    return sharedPreferences.getString(key, null)
}

fun Context.clearFromSharedPreferences(key: String) {
    val sharedPreferences = getSharedPreferences("EftaaPayPrefs", Context.MODE_PRIVATE)
    sharedPreferences.edit() {
        remove(key)
    }
}

fun Context.clearAllSharedPreferences() {
    val sharedPreferences = getSharedPreferences("EftaaPayPrefs", Context.MODE_PRIVATE)
    sharedPreferences.edit() {
        clear()
    }
}

fun Context.getUserData(): LoginData? {
    val userDataJson = getFromSharedPreferences("USER_DATA")
    return if (userDataJson != null) {
        Gson().fromJson(userDataJson, LoginData::class.java)
    } else {
        null
    }
}

fun Context.getUserTerminalData(): PaymentTerminal? {
    val userTerminalDataJson = getFromSharedPreferences("USER_TERMINAL_DATA")
    return if (userTerminalDataJson != null) {
        Gson().fromJson(userTerminalDataJson, PaymentTerminal::class.java)
    } else {
        null
    }
}

fun Context.getUserParentData(): UserData? {
    val userParentDataJson = getFromSharedPreferences("USER_PARENT_DATA")
    return if (userParentDataJson != null) {
        Gson().fromJson(userParentDataJson, UserData::class.java)
    } else {
        null
    }
}

fun Context.getUserDevicePEDData(): PedStatusResponse? {
    val userDevicePEDDataJson = getFromSharedPreferences("USER_DEVICE_PED_STATUS")
    return if (userDevicePEDDataJson != null) {
        Gson().fromJson(userDevicePEDDataJson, PedStatusResponse::class.java)
    } else {
        null
    }
}

fun Context.getStoredCurrencies(): List<Map<String, Any>>? {
    val currencyJson = getFromSharedPreferences("CURRENCY_DATA") ?: return null
    val gson = Gson()
    val type = object : TypeToken<List<Map<String, Any>>>() {}.type
    return gson.fromJson(currencyJson, type)
}

fun Context.isTokenExpired(): Boolean {
    val expiryTimeString = getFromSharedPreferences("CONNECTION_TOKEN_EXPIRY") ?: return true

    try {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val expiryUtcTime = LocalDateTime.parse(expiryTimeString, formatter)
        val expiryZonedUtcTime = expiryUtcTime.atZone(ZoneId.of("UTC"))

        val userZonedTime = expiryZonedUtcTime.withZoneSameInstant(ZoneId.systemDefault())

        val currentTime = ZonedDateTime.now(ZoneId.systemDefault())

        Log.e("Real Expiry", userZonedTime.toString());

        return currentTime.isAfter(userZonedTime)
    } catch (e: Exception) {
        e.printStackTrace()
        return true
    }
}

fun getExpiryTimeFromToken(token: String?): Long? {
    return try {
        if (token == null) return null
        val parts = token.split(".")
        if (parts.size != 3) return null

        val payload = String(android.util.Base64.decode(parts[1], android.util.Base64.URL_SAFE or android.util.Base64.NO_PADDING))
        val json = org.json.JSONObject(payload)
        json.optLong("exp") * 1000
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun isSessionValid(context: Context): Boolean {
    val userData = context.getUserData()
    val tokenExpiry = userData?.tokenExpiry ?: 0
    Log.e("Token Expiry", SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date(tokenExpiry)));

    return System.currentTimeMillis() < tokenExpiry
}

fun handleSessionExpiry(context: Context) {
    context.clearAllSharedPreferences()
    Toast.makeText(context, context.getString(R.string.session_expired_message), Toast.LENGTH_SHORT).show()

    val intent = Intent(context, LoginScreen::class.java).apply {
        flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
    }
    context.startActivity(intent)
}

fun isBiometricAvailable(context: Context): Boolean {
    val biometricManager = BiometricManager.from(context)
    return when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)) {
        BiometricManager.BIOMETRIC_SUCCESS -> true
        BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
            false
        }
        BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> {
            false
        }
        BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> {
            false
        }
        else -> false
    }
}

fun showBiometricPrompt(
    context: Context,
    onSuccess: () -> Unit,
    onError: (String) -> Unit
) {
    val executor = ContextCompat.getMainExecutor(context)
    val biometricPrompt = BiometricPrompt(
        context as androidx.fragment.app.FragmentActivity,
        executor,
        object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                onSuccess()
            }

            override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                onError(errString.toString())
            }

            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                onError("Authentication failed. Please try again.")
            }
        }
    )

    val promptInfo = BiometricPrompt.PromptInfo.Builder()
        .setTitle("EftaaPay Biometric Authentication")
        .setSubtitle("Use your fingerprint or face to authenticate")
        .setDescription("For secure access, please use biometric authentication.")
        .setNegativeButtonText("Cancel")
        .setAllowedAuthenticators(
            BiometricManager.Authenticators.BIOMETRIC_STRONG or
                    BiometricManager.Authenticators.BIOMETRIC_WEAK
        )
        .build()

    biometricPrompt.authenticate(promptInfo)
}


fun Context.setBiometricEnabled(enabled: Boolean) {
    saveToSharedPreferences("BIOMETRIC_ENABLED", enabled.toString())
}

fun Context.isBiometricEnabled(): Boolean {
    return getFromSharedPreferences("BIOMETRIC_ENABLED")?.toBoolean() ?: false
}

fun authenticateWithBiometrics(context: Context, onSuccess: () -> Unit, onFailure: (String) -> Unit) {
    if (context.isBiometricEnabled()) {
        showBiometricPrompt(
            context = context,
            onSuccess = {
                val userData = context.getUserData()
                val sessionToken = userData?.token

                if (sessionToken != null && isSessionValid(context)) {
                    onSuccess()
                } else {
                    onFailure("Session token not found or session expired.")
                }
            },
            onError = { errorMessage ->
                onFailure(errorMessage)
            }
        )
    } else {
        onFailure("Biometric authentication is not enabled.")
    }
}

fun enforceOrientation(activity: Activity) {
    val metrics = Resources.getSystem().displayMetrics
    val widthInches = metrics.widthPixels / metrics.xdpi
    val heightInches = metrics.heightPixels / metrics.ydpi
    val diagonalInches = sqrt(widthInches * widthInches + heightInches * heightInches)
    val isTablet = diagonalInches >= 7.0

    if (isTablet) {
        activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED // Allow both orientations
    } else {
        activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED // Lock to portrait
    }
}

fun copyDeviceIdToClipboard(context: Context) {
    val deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)

    if (deviceId.isNullOrEmpty()) {
        Toast.makeText(context, "Failed to retrieve device ID", Toast.LENGTH_LONG).show()
        return
    }

    val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clip = ClipData.newPlainText("Device ID", deviceId)
    clipboard.setPrimaryClip(clip)
}

fun Context.convertToUserTimeZone(transactionDateTime: String): String? {
    return try {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

        val cetDateTime = LocalDateTime.parse(transactionDateTime, formatter)

        val cetZonedTime = cetDateTime.atZone(ZoneId.of("CET"))

        val userZonedTime = cetZonedTime.withZoneSameInstant(ZoneId.systemDefault())

        userZonedTime.format(formatter)
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun checkAndUpdateApp(activity: Activity, onUpdateFailed: (Boolean) -> Unit) {
    val appUpdateManager: AppUpdateManager = AppUpdateManagerFactory.create(activity)

    val appUpdateInfoTask = appUpdateManager.appUpdateInfo

    appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
        if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE &&
            appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)
        ) {
            try {
                appUpdateManager.startUpdateFlowForResult(
                    appUpdateInfo,
                    AppUpdateType.IMMEDIATE,
                    activity,
                    1001
                )
                onUpdateFailed(true)
            } catch (e: Exception) {
                e.printStackTrace()
                onUpdateFailed(false)
            }
        }
    }.addOnFailureListener {
        onUpdateFailed(false)
    }
}

fun openPlayStore(context: Context) {
    try {
        val intent = Intent(Intent.ACTION_VIEW, "market://details?id=${context.packageName}".toUri())
        context.startActivity(intent)
    } catch (e: Exception) {
        Toast.makeText(context, "Unable to open Play Store", Toast.LENGTH_SHORT).show()
    }
}

fun createProfileName(email: String): String {
    try {
        val localPart = email.split("@")[0]

        val names: List<String> = if ('.' in localPart) {
            localPart.split('.')
        } else {
            val result = mutableListOf<String>()
            var currentName = ""
            for (char in localPart) {
                if (char.isUpperCase() && currentName.isNotEmpty() && result.isNotEmpty()) {
                    result.add(currentName)
                    currentName = char.toString()
                } else {
                    currentName += char
                }
            }
            if (currentName.isNotEmpty()) {
                result.add(currentName)
            }
            if (result.size < 2) {
                listOf(localPart.take(1), localPart.drop(1)).filter { it.isNotEmpty() }
            } else {
                result
            }
        }

        if (names.size < 2) {
            return "Unknown User"
        }

        val firstName = names[0].replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
        val lastName = names[1].replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }

        return "$firstName $lastName"
    } catch (e: Exception) {
        return "Unknown User"
    }
}

