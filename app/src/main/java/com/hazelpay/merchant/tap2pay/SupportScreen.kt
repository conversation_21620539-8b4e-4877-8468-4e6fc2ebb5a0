package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.Spinner
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.hazelpay.merchant.tap2pay.utils.createSupportIssue
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import kotlinx.coroutines.launch

class SupportScreen : AppCompatActivity() {
    private lateinit var editTextSummary: EditText
    private lateinit var editTextDescription: EditText
    private lateinit var spinnerIssueType: Spinner
    private lateinit var buttonSubmit: Button
    private lateinit var backButton: ImageButton


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        enforceOrientation(this)
        setContentView(R.layout.activity_support_screen)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        editTextSummary = findViewById(R.id.editText_summary)
        editTextDescription = findViewById(R.id.editText_description)
        spinnerIssueType = findViewById(R.id.spinner_issue_type)
        buttonSubmit = findViewById(R.id.button_submit)
        backButton = findViewById(R.id.backButton_bug)


        val issueTypes = listOf("Task")
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, issueTypes)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerIssueType.adapter = adapter

        backButton.setOnClickListener{
            val intent = Intent(this, SettingsScreen::class.java)
            startActivity(intent)
            finish()
        }

        buttonSubmit.setOnClickListener {
            val summary = editTextSummary.text.toString().trim()
            val description = editTextDescription.text.toString().trim()
            val selectedIssueType = spinnerIssueType.selectedItem.toString()

            if (summary.isEmpty() || description.isEmpty() || selectedIssueType.isEmpty()) {
                Toast.makeText(this, getString(R.string.allchecks), Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            showLoader()

            lifecycleScope.launch {
                val (success, message) = createSupportIssue(
                    context = this@SupportScreen,
                    summary = summary,
                    text = description,
                )

                if (success) {
                    hideLoader()
                    Toast.makeText(this@SupportScreen, message, Toast.LENGTH_LONG).show()
                    finish()
                    startActivity(Intent(this@SupportScreen, SettingsScreen::class.java))
                } else {
                    hideLoader()
                    Toast.makeText(this@SupportScreen, message, Toast.LENGTH_LONG).show()
                    finish()
                    startActivity(Intent(this@SupportScreen, SettingsScreen::class.java))
                }
            }

            }
    }

    private fun showLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.VISIBLE
    }

    private fun hideLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.GONE
    }
}