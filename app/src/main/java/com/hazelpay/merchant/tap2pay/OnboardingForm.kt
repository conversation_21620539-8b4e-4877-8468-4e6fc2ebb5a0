package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.*
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.hazelpay.merchant.tap2pay.utils.countries
import com.hazelpay.merchant.tap2pay.utils.emailPattern
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.expectedMonthlyActiveTerminals
import com.hazelpay.merchant.tap2pay.utils.expectedTransactionsPerMonth
import com.hazelpay.merchant.tap2pay.utils.merchantTypes
import com.hazelpay.merchant.tap2pay.utils.phonePattern
import com.hazelpay.merchant.tap2pay.utils.sendOnboardingRequest
import kotlinx.coroutines.launch

class OnboardingForm : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        enforceOrientation(this)
        setContentView(R.layout.activity_onboarding_form)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        val termsOfUse = findViewById<TextView>(R.id.termsOfUse)
        val emailEditText = findViewById<EditText>(R.id.email)
        val merchantTypeSpinner = findViewById<Spinner>(R.id.merchantType)
        val countrySpinner = findViewById<Spinner>(R.id.country)
        val companyNameEditText = findViewById<EditText>(R.id.companyName)
        val expectedVolumeSpinner = findViewById<Spinner>(R.id.expectedVolume)
        val expectedTerminalsSpinner = findViewById<Spinner>(R.id.expectedTerminals)
        val phoneNumberEditText = findViewById<EditText>(R.id.phoneNumber)
        val agreeTermsCheckBox = findViewById<CheckBox>(R.id.agreeTerms)
        val submitButton = findViewById<LinearLayout>(R.id.ll_submit_btn)
        val backButton = findViewById<ImageButton>(R.id.backButton_onboarding)

        merchantTypeSpinner.adapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, merchantTypes.keys.toList())
        countrySpinner.adapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, countries)
        expectedVolumeSpinner.adapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, expectedTransactionsPerMonth.keys.toList())
        expectedTerminalsSpinner.adapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, expectedMonthlyActiveTerminals.keys.toList())

        backButton.setOnClickListener {
            val intent = Intent(this@OnboardingForm, LoginScreen::class.java)
            startActivity(intent)
            finish()
        }

        termsOfUse.setOnClickListener {
            val url = "https://onboarding.eftaapay.com/termsofuse"
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            startActivity(intent)
        }

        submitButton.setOnClickListener {
            val email = emailEditText.text.toString().trim()
            val companyName = companyNameEditText.text.toString().trim()
            val phoneNumber = phoneNumberEditText.text.toString().trim()
            val isTermsAgreed = agreeTermsCheckBox.isChecked

            val merchantTypeKey = merchantTypeSpinner.selectedItem?.toString() ?: ""
            val merchantType = merchantTypes[merchantTypeKey] ?: ""

            val countryKey = countrySpinner.selectedItem?.toString() ?: ""
            val country = countries.find { it == countryKey } ?: ""

            val expectedTransactionsKey = expectedVolumeSpinner.selectedItem?.toString() ?: ""
            val expectedTransactions = expectedTransactionsPerMonth[expectedTransactionsKey] ?: ""

            val expectedTerminalsKey = expectedTerminalsSpinner.selectedItem?.toString() ?: ""
            val expectedTerminals = expectedMonthlyActiveTerminals[expectedTerminalsKey] ?: ""

            if (!email.matches(emailPattern)) {
                Toast.makeText(this, "Invalid email address", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            if (!phoneNumber.matches(phonePattern)) {
                Toast.makeText(this, "Invalid phone number", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            if (email.isEmpty() || companyName.isEmpty() || phoneNumber.isEmpty() ||
                merchantTypeKey.equals("Select", ignoreCase = true) || merchantType.isEmpty() ||
                countryKey.equals("Select", ignoreCase = true) || country.isEmpty() ||
                expectedTransactionsKey.equals("Select", ignoreCase = true) || expectedTransactions.isEmpty() ||
                expectedTerminalsKey.equals("Select", ignoreCase = true) || expectedTerminals.isEmpty() ||
                !isTermsAgreed) {

                Toast.makeText(this, getString(R.string.allcheckswithterms), Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            showLoader()

            lifecycleScope.launch {
                val (success, message) = sendOnboardingRequest(
                    email = email,
                    merchantType = merchantType,
                    country = country,
                    companyName = companyName,
                    phone = phoneNumber,
                    expectedTransactionsPerMonth = expectedTransactions,
                    expectedMonthlyActiveTerminals = expectedTerminals
                )

                if (success) {
                    hideLoader()
                    Toast.makeText(this@OnboardingForm, message, Toast.LENGTH_LONG).show()
                    finish()
                    startActivity(Intent(this@OnboardingForm, LoginScreen::class.java))
                } else {
                    hideLoader()
                    Toast.makeText(this@OnboardingForm, message, Toast.LENGTH_LONG).show()
                    finish()
                    startActivity(Intent(this@OnboardingForm, LoginScreen::class.java))
                }
            }
        }    }

    private fun showLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.VISIBLE
    }

    private fun hideLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.GONE
    }

}