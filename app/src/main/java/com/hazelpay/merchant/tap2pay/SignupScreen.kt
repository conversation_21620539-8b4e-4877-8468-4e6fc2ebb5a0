package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.util.Patterns
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation

class SignupScreen : AppCompatActivity() {

    private lateinit var emailEditText: EditText
    private lateinit var passwordEditText: EditText
    private lateinit var confirmPasswordEditText: EditText
    private lateinit var passwordToggle: ImageView
    private lateinit var confirmPasswordToggle: ImageView
    private lateinit var signUpButton: ImageButton
    private lateinit var loginText: TextView

    private var isPasswordVisible = false
    private var isConfirmPasswordVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_signup_screen)
        enforceOrientation(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        emailEditText = findViewById(R.id.email_address)
        passwordEditText = findViewById(R.id.password)
        confirmPasswordEditText = findViewById(R.id.confirm_password)
        passwordToggle = findViewById(R.id.password_toggle)
        confirmPasswordToggle = findViewById(R.id.confirm_password_toggle)
        signUpButton = findViewById(R.id.sign_up_button)
        loginText = findViewById(R.id.login_text)

        passwordToggle.setOnClickListener {
            togglePasswordVisibility()
        }

        confirmPasswordToggle.setOnClickListener {
            toggleConfirmPasswordVisibility()
        }

        signUpButton.setOnClickListener {
            val email = emailEditText.text.toString().trim()
            val password = passwordEditText.text.toString().trim()
            val confirmPassword = confirmPasswordEditText.text.toString().trim()

            if (validateInput(email, password, confirmPassword)) {
                val intent = Intent(this, ProfileUpdate::class.java)
                startActivity(intent)
                finish()
            }
        }

        loginText.setOnClickListener {
            val intent = Intent(this, LoginScreen::class.java)
            startActivity(intent)
            finish()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun togglePasswordVisibility() {
        if (isPasswordVisible) {
            passwordEditText.transformationMethod = PasswordTransformationMethod.getInstance()
            passwordToggle.setImageResource(android.R.drawable.ic_menu_view)
        } else {
            passwordEditText.transformationMethod = HideReturnsTransformationMethod.getInstance()
            passwordToggle.setImageResource(android.R.drawable.ic_menu_view)
        }
        isPasswordVisible = !isPasswordVisible
        passwordEditText.setSelection(passwordEditText.text.length)
    }

    private fun toggleConfirmPasswordVisibility() {
        if (isConfirmPasswordVisible) {
            confirmPasswordEditText.transformationMethod = PasswordTransformationMethod.getInstance()
            confirmPasswordToggle.setImageResource(android.R.drawable.ic_menu_view)
        } else {
            confirmPasswordEditText.transformationMethod = HideReturnsTransformationMethod.getInstance()
            confirmPasswordToggle.setImageResource(android.R.drawable.ic_menu_view)
        }
        isConfirmPasswordVisible = !isConfirmPasswordVisible
        confirmPasswordEditText.setSelection(confirmPasswordEditText.text.length)
    }

    private fun validateInput(email: String, password: String, confirmPassword: String): Boolean {
        if (email.isEmpty()) {
            showToast(getString(R.string.email_cannot_be_empty))
            return false
        }
        if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            showToast(getString(R.string.invalid_email_address))
            return false
        }
        if (password.isEmpty()) {
            showToast(getString(R.string.password_cannot_be_empty))
            return false
        }
        if (confirmPassword.isEmpty()) {
            showToast(getString(R.string.confirm_password_empty))
            return false
        }

        val passwordPattern = Regex("^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[^A-Za-z\\d]).{8,}\$")
        if (!passwordPattern.matches(password)) {
            showToast(getString(R.string.invalid_password_format))
            return false
        }

        if (password != confirmPassword) {
            showToast(getString(R.string.passwords_do_not_match))
            return false
        }
        return true
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
