package com.hazelpay.merchant.tap2pay

import android.app.DatePickerDialog
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hazelpay.merchant.tap2pay.model.BusinessDayTransaction
import com.hazelpay.merchant.tap2pay.adapter.TransactionAdapter
import com.hazelpay.merchant.tap2pay.adapter.TransactionItem
import com.hazelpay.merchant.tap2pay.utils.ConnectivityChecker
import com.hazelpay.merchant.tap2pay.utils.fetchArchivedTransactionData
import com.hazelpay.merchant.tap2pay.utils.getCurrencySymbolById
import com.hazelpay.merchant.tap2pay.utils.getFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.getUserTerminalData
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class ReportsScreen : AppCompatActivity() {

    private var currentPage = 1
    private var isLoading = false
    private var hasMoreData = true
    private val transactionList = mutableListOf<TransactionItem.Transaction>()
    private lateinit var transactionAdapter: TransactionAdapter
    private lateinit var connectivityChecker: ConnectivityChecker


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_report_screen)

        val startDateText: TextView = findViewById(R.id.start_date)
        val endDateText: TextView = findViewById(R.id.end_date)
        val searchButton: ImageButton = findViewById(R.id.search_button)
        connectivityChecker = ConnectivityChecker(this)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

        val calendar = Calendar.getInstance()
        val today = calendar.time

        calendar.add(Calendar.DAY_OF_YEAR, -1)
        val yesterday = calendar.time
        endDateText.text = dateFormat.format(today)

        startDateText.text = dateFormat.format(yesterday)
        endDateText.text = dateFormat.format(yesterday)

        startDateText.setOnClickListener {
            showDatePicker { selectedDate ->
                startDateText.text = dateFormat.format(selectedDate)
            }
        }

        endDateText.setOnClickListener {
            showDatePicker { selectedDate ->
                endDateText.text = dateFormat.format(selectedDate)
            }
        }

        searchButton.setOnClickListener {
            val startDate = startDateText.text.toString()
            val endDate = endDateText.text.toString()

            if (startDate.isEmpty() || endDate.isEmpty()) {
                Toast.makeText(this, "Please select both start and end dates", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            try {
                val parsedStartDate = dateFormat.parse(startDate)
                val parsedEndDate = dateFormat.parse(endDate)

                if (parsedStartDate != null) {
                    if (parsedStartDate.after(parsedEndDate)) {
                        Toast.makeText(this, "Start date cannot be after end date", Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }
                }

                currentPage = 1
                hasMoreData = true
                transactionList.clear()
                transactionAdapter.notifyDataSetChanged()
                showLoader()
                fetchTransactions(startDate, endDate)

            } catch (e: Exception) {
                Toast.makeText(this, "Invalid date format", Toast.LENGTH_SHORT).show()
            }
        }

        setupRecyclerView()
        fetchTransactions(startDateText.text.toString(), endDateText.text.toString())
        setupBottomNavigation()
    }

    private fun showLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.VISIBLE
    }

    private fun hideLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.GONE
    }

    private fun showDatePicker(onDateSelected: (Date) -> Unit) {
        val calendar = Calendar.getInstance()
        DatePickerDialog(
            this,
            R.style.CustomDatePicker,
            { _, year, month, dayOfMonth ->
                val selectedDate = Calendar.getInstance().apply {
                    set(Calendar.YEAR, year)
                    set(Calendar.MONTH, month)
                    set(Calendar.DAY_OF_MONTH, dayOfMonth)
                }.time
                onDateSelected(selectedDate)
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun setupBottomNavigation() {
        val bottomNavigationView: BottomNavigationView = findViewById(R.id.bottom_navigation)
        bottomNavigationView.labelVisibilityMode = NavigationBarView.LABEL_VISIBILITY_SELECTED
        bottomNavigationView.selectedItemId = R.id.reports

        val isOpen = getFromSharedPreferences("Terminal_access").toBoolean()
        bottomNavigationView.menu.findItem(R.id.sales).isEnabled = isOpen

        bottomNavigationView.setOnNavigationItemSelectedListener { item ->
            when (item.itemId) {
                R.id.home -> {
                    startActivity(Intent(this, HomeScreen::class.java))
                    finish()
                    true
                }
                R.id.sales -> {
                    startActivity(Intent(this, PaymentScreen::class.java))
                    finish()
                    true
                }
                R.id.reports -> {
                    true
                }
                R.id.settings ->{
                    startActivity(Intent(this, SettingsScreen::class.java))
                    finish()
                    true
                }
                else -> false
            }
        }
    }

    private fun fetchTransactions(startDate: String?, endDate: String?) {
        val terminalId = getUserTerminalData()?.terminalId

        if (isLoading) return
        isLoading = true
        showLoader()

        if (terminalId != null) {
            fetchArchivedTransactionData(
                context = this,
                lifecycleScope = lifecycleScope,
                sort = "-date_time",
                startDate = startDate,
                endDate = endDate,
                page = currentPage,
                terminalId
            ) { transactions, paginationData ->
                isLoading = false
                hideLoader()

                val recyclerView: RecyclerView = findViewById(R.id.transaction_recycler_view)
                val emptyStateLayout: LinearLayout = findViewById(R.id.empty_state_layout)
                val txtIncome: TextView = findViewById(R.id.income_value)
                val txtTransaction: TextView = findViewById(R.id.transactions_value)
                val currencySymbol = getUserParentData()?.currency!!

                if (!transactions.isNullOrEmpty()) {
                    recyclerView.visibility = View.VISIBLE
                    emptyStateLayout.visibility = View.GONE

                    val newTransactions = mapTransactions(transactions)
                    transactionList.addAll(newTransactions)
                    transactionAdapter.notifyItemRangeInserted(
                        transactionList.size - newTransactions.size,
                        newTransactions.size
                    )

                    val totalIncome = transactionList.filter { it.transactionStatus == 1 && it.originTransactionId == 0 }
                        .sumOf { it.amount.toDouble() }
                    txtIncome.text = "$currencySymbol ${String.format("%.2f", totalIncome)}"
                    txtTransaction.text = transactionList.size.toString()

                    hasMoreData = paginationData?.currentPage ?: 0 < (paginationData?.pageCount ?: 0)
                } else {
                    hasMoreData = false
                    if (transactionList.isEmpty()) {
                        recyclerView.visibility = View.GONE
                        emptyStateLayout.visibility = View.VISIBLE
                        txtIncome.text = "$currencySymbol 0.00"
                        txtTransaction.text = "0"
                    }
                }
            }
        }
    }

    private fun setupRecyclerView() {
        val recyclerView: RecyclerView = findViewById(R.id.transaction_recycler_view)

        transactionAdapter = TransactionAdapter(
            transactions = transactionList,
            context = this,
            lifecycleOwner = this,
            isRefund = true,
            refreshCallback = { fetchTransactions(null, null) }
        )

        recyclerView.adapter = transactionAdapter
        recyclerView.layoutManager = LinearLayoutManager(this)

        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val totalItemCount = layoutManager.itemCount
                val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                if (!isLoading && hasMoreData && lastVisibleItemPosition + 5 >= totalItemCount) {
                    currentPage++
                    fetchTransactions(
                        findViewById<TextView>(R.id.start_date).text.toString(),
                        findViewById<TextView>(R.id.end_date).text.toString()
                    )
                }
            }
        })
    }

    private fun mapTransactions(
        transactions: List<BusinessDayTransaction>
    ): List<TransactionItem.Transaction> {
        return transactions.map { transaction ->
            TransactionItem.Transaction(
                transactionId = transaction.id,
                rrn = transaction.rrn,
                cardMask = transaction.card,
                amount = transaction.amount,
                currencySymbol = getCurrencySymbolById(transaction.currency_id),
                dateTime = transaction.date_time,
                transactionStatus = transaction.mpos_transaction_status_id,
                originTransactionId = transaction.origin_trx_id
            )
        }
    }

    override fun onStart() {
        super.onStart()
        connectivityChecker.startListening()
    }

    override fun onStop() {
        super.onStop()
        connectivityChecker.stopListening()
    }
}

