package com.hazelpay.merchant.tap2pay

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.view.WindowManager
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.hazelpay.merchant.tap2pay.model.BusinessDayTransaction
import com.hazelpay.merchant.tap2pay.model.PaginationData
import com.hazelpay.merchant.tap2pay.model.PaymentTerminal
import com.hazelpay.merchant.tap2pay.model.PedStatusResponse
import com.hazelpay.merchant.tap2pay.adapter.TransactionAdapter
import com.hazelpay.merchant.tap2pay.adapter.TransactionItem
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.hazelpay.merchant.tap2pay.utils.ConnectivityChecker
import com.hazelpay.merchant.tap2pay.utils.ResponseCodes
import com.hazelpay.merchant.tap2pay.utils.SdkUtils
import com.hazelpay.merchant.tap2pay.utils.checkAndUpdateApp
import com.hazelpay.merchant.tap2pay.utils.closeBusinessDay
import com.hazelpay.merchant.tap2pay.utils.createProfileName
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.fetchAndStoreUserData
import com.hazelpay.merchant.tap2pay.utils.fetchAuthToken
import com.hazelpay.merchant.tap2pay.utils.fetchBusinessDayStatus
import com.hazelpay.merchant.tap2pay.utils.fetchConnectionToken
import com.hazelpay.merchant.tap2pay.utils.fetchTransactionDataForCurrentDay
import com.hazelpay.merchant.tap2pay.utils.getCurrencySymbolById
import com.hazelpay.merchant.tap2pay.utils.getFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.getUserData
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import com.hazelpay.merchant.tap2pay.utils.getUserTerminalData
import com.hazelpay.merchant.tap2pay.utils.handleSessionExpiry
import com.hazelpay.merchant.tap2pay.utils.isSessionValid
import com.hazelpay.merchant.tap2pay.utils.openPlayStore
import com.hazelpay.merchant.tap2pay.utils.saveToSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.unbindPaymentTerminal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class HomeScreen : AppCompatActivity() {

    private companion object {
        private const val MERCHANT_BANK_ID = BuildConfig.MERCHANT_BANK_ID
        private const val SOLUTION_PARTNER_ID = BuildConfig.SOLUTION_PARTNER_ID
        private const val APP_LANGUAGE_CODE = "en"
    }

    private var currentPage = 1
    private var isLoading = false
    private var hasMoreData = true

    private lateinit var transactionAdapter: TransactionAdapter
    private val transactionList = mutableListOf<TransactionItem.Transaction>()

    private lateinit var closeButton: Button
    private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private lateinit var bottomNavigationView: BottomNavigationView
    private lateinit var connectivityChecker: ConnectivityChecker


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_home_screen)
        enforceOrientation(this)
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout)
        bottomNavigationView = findViewById(R.id.bottom_navigation)
        connectivityChecker = ConnectivityChecker(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        setupRecyclerView()
        setupBottomNavigation()
        setupCloseButton()
        showLoader()

        findViewById<TextView>(R.id.txt_username_home).text =
            if (getUserData()?.user?.firstName.isNullOrEmpty() || getUserData()?.user?.lastName.isNullOrEmpty()) "Welcome to Merchant App " else "Hi ${getUserData()?.user?.firstName} ${getUserData()?.user?.lastName} 👋"

        swipeRefreshLayout.setColorSchemeResources(R.color.lavender)

        swipeRefreshLayout.setOnRefreshListener {
            lifecycleScope.launch {
                refreshContent()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        connectivityChecker.startListening()
    }

    override fun onStop() {
        super.onStop()
        connectivityChecker.stopListening()
    }

    private suspend fun refreshContent() {
        showLoader()
        getAndStoreUserPEDDetails(this@HomeScreen)
        checkAndDisplayBusinessDayStatus(this@HomeScreen)
        refreshTransactionList()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    override fun onResume() {
        super.onResume()
        checkAndUpdateApp(this) { success ->
            if (!success) {
                openPlayStore(this)
            }
        }
        lifecycleScope.launch {
            refreshContent()
        }
    }

    private fun setupCloseButton() {
        closeButton = findViewById(R.id.sdk_status_close_button)
        closeButton.setOnClickListener {
            val sdkStatusOverlay = findViewById<FrameLayout>(R.id.sdk_status_overlay)
            sdkStatusOverlay.visibility = View.GONE
        }
    }

    private fun setupRecyclerView() {
        val recyclerView: RecyclerView = findViewById(R.id.transaction_recycler_view)

        transactionAdapter = TransactionAdapter(
            transactions = transactionList,
            context = this,
            lifecycleOwner = this
        ) {
            resetAndFetchTransactions()
        }

        recyclerView.adapter = transactionAdapter
        recyclerView.layoutManager = LinearLayoutManager(this)

        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(rv: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(rv, dx, dy)
                val layoutManager = rv.layoutManager as LinearLayoutManager
                val totalItemCount = layoutManager.itemCount
                val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                if (!isLoading && hasMoreData && lastVisibleItemPosition + 5 >= totalItemCount) {
                    currentPage++
                    fetchTransactionData()
                }
            }
        })
    }

    private fun setupBottomNavigation() {
        bottomNavigationView.labelVisibilityMode = NavigationBarView.LABEL_VISIBILITY_SELECTED
        bottomNavigationView.selectedItemId = R.id.home

        bottomNavigationView.setOnNavigationItemSelectedListener { item ->
            when (item.itemId) {
                R.id.home -> true
                R.id.sales -> {
                    startActivity(Intent(this, PaymentScreen::class.java))
                    finish()
                    true
                }

                R.id.reports -> {
                    startActivity(Intent(this, ReportsScreen::class.java))
                    finish()
                    true
                }

                R.id.settings -> {
                    startActivity(Intent(this, SettingsScreen::class.java))
                    finish()
                    true
                }

                else -> false
            }
        }

    }

    private fun updateSalesOverlayVisibility() {
        val isOpen = getFromSharedPreferences("Terminal_access").toBoolean()
        bottomNavigationView.menu.findItem(R.id.sales).isEnabled = isOpen
    }

    private fun fetchTransactionData() {
        val terminalId = getUserTerminalData()?.terminalId

        if (isLoading || !hasMoreData) return

        isLoading = true

        if (terminalId != null) {
            fetchTransactionDataForCurrentDay(
                context = this,
                lifecycleScope = lifecycleScope,
                sort = "-date_time",
                page = currentPage,
                terminalId = terminalId
            ) { transactions, paginationData ->
                try {
                    isLoading = false

                    if (!transactions.isNullOrEmpty()) {
                        val newTransactions = mapTransactions(transactions)
                        transactionList.addAll(newTransactions)

                        transactionAdapter.notifyItemRangeInserted(
                            transactionList.size - newTransactions.size,
                            newTransactions.size
                        )

                        updateUIWithTransactions(transactionList, paginationData)
                    } else {
                        handleEmptyState()
                    }

                } catch (e: Exception) {
                    hideLoader()
                    Toast.makeText(
                        this,
                        "Failed to load transactions: ${e.message}",
                        Toast.LENGTH_LONG
                    ).show()
                }
            }
        }
    }

    private fun resetAndFetchTransactions() {
        currentPage = 1
        hasMoreData = true
        transactionList.clear()
        transactionAdapter.notifyDataSetChanged()
        fetchTransactionData()
    }

    private fun mapTransactions(transactions: List<BusinessDayTransaction>): List<TransactionItem.Transaction> {
        return transactions.map { transaction ->
            TransactionItem.Transaction(
                transactionId = transaction.id,
                rrn = transaction.rrn,
                cardMask = transaction.card,
                amount = transaction.amount,
                currencySymbol = getCurrencySymbolById(transaction.currency_id),
                dateTime = transaction.date_time,
                transactionStatus = transaction.mpos_transaction_status_id,
                originTransactionId = transaction.origin_trx_id
            )
        }
    }

    private fun updateUIWithTransactions(
        currentList: List<TransactionItem.Transaction>,
        paginationData: PaginationData?
    ) {
        val incomeValueMain: TextView = findViewById(R.id.income_value_main)
        val incomeValueDecimal: TextView = findViewById(R.id.income_value_decimal)
        val transactionListHeader: TextView = findViewById(R.id.transaction_list_header)
        val txtCurrency: TextView = findViewById(R.id.txt_currency_home)

        val totalIncome = currentList.filter { it.transactionStatus == 1 && it.originTransactionId == 0 }
            .sumOf { it.amount.toDouble() }

        val incomeString = String.format("%.2f", totalIncome)
        val (mainPart, decimalPart) = incomeString.split(".")
        val transactionCount = currentList.size.toString()

        txtCurrency.text = getUserParentData()?.currency!!
        incomeValueMain.text = mainPart
        incomeValueDecimal.text = ".$decimalPart"
        transactionListHeader.text = "Unbilled Transactions ($transactionCount)"

        hasMoreData = (paginationData?.currentPage ?: 0) < (paginationData?.pageCount ?: 0)
        hideLoader()
    }

    private fun handleEmptyState() {
        val recyclerView: RecyclerView = findViewById(R.id.transaction_recycler_view)
        val emptyStateLayout: LinearLayout = findViewById(R.id.empty_state_layout)
        val incomeValueMain: TextView = findViewById(R.id.income_value_main)
        val incomeValueDecimal: TextView = findViewById(R.id.income_value_decimal)
        val txtCurrency: TextView = findViewById(R.id.txt_currency_home)
        val transactionListHeader: TextView = findViewById(R.id.transaction_list_header)

        if (transactionList.isEmpty()) {
            recyclerView.visibility = View.GONE
            emptyStateLayout.visibility = View.VISIBLE
            incomeValueMain.text = "0"
            incomeValueDecimal.text = ".00"
            txtCurrency.text = getUserParentData()?.currency!!
            transactionListHeader.text = "Unbilled Transactions"
            hasMoreData = false
        } else {
            recyclerView.visibility = View.VISIBLE
            emptyStateLayout.visibility = View.GONE
        }
        hideLoader()
    }

    private suspend fun checkAndDisplayBusinessDayStatus(context: Context): Pair<Boolean, Int> {
        val userTerminalData = getUserTerminalData()
        val terminalId = userTerminalData?.terminalName

        val businessDayStatus = terminalId?.let {
            fetchBusinessDayStatus(
                context,
                MERCHANT_BANK_ID,
                "merchant_email",
                getUserParentData()?.email!!,
                "merchant_terminal_id",
                it
            )
        }

        return withContext(Dispatchers.Main) {
            val terminalStatus: TextView = findViewById(R.id.txt_business_day_status)

            if (businessDayStatus != null) {
                swipeRefreshLayout.isRefreshing = false
                when (businessDayStatus.data?.code) {
                    4 -> {
                        terminalStatus.visibility = View.GONE
                        Pair(false, -1)
                    }

                    1 -> {
                        terminalStatus.visibility = View.GONE
                        Pair(true, -1)
                    }

                    else -> {
                        terminalStatus.visibility = View.VISIBLE
                        terminalStatus.text = "Business Day Closing in Progress..."
                        terminalStatus.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.transaction_card_red
                            )
                        )
                        Pair(false, businessDayStatus.data?.code ?: -1)
                    }
                }
            } else {
                swipeRefreshLayout.isRefreshing = false
                Pair(false, -1)
            }
        }
    }

    private suspend fun getAndStoreUserPEDDetails(context: Context) {
        val deviceId =
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        val terminalStatus: TextView = findViewById(R.id.txt_connection_status_value)
        val terminalInfo: LinearLayout = findViewById(R.id.terminal_info)
        val terminalName: TextView = findViewById(R.id.txt_terminal_name_value)
        val terminalBtn: ImageButton = findViewById(R.id.btn_terminal)

        val (isPEDLinked, pedData) = fetchAndStoreUserData(context)
        val userTerminalData = getUserTerminalData()

        if (!isPEDLinked) {
            saveToSharedPreferences("Terminal_access", "false")
            updateSalesOverlayVisibility()

            setupUnpairedDeviceUI(terminalBtn, terminalStatus, terminalInfo)
            terminalBtn.setOnClickListener {
                startTerminalLinkProcess(context, userTerminalData)
            }
        } else {
            // Device is paired
            val pedDeviceId = pedData?.merchant_device_id?.substringBefore("_")

            if (pedDeviceId == deviceId) {
                // Device ID matches - normal scenario
                setupPairedDeviceUI(
                    terminalBtn,
                    terminalStatus,
                    terminalInfo,
                    pedData,
                    terminalName
                )
                saveToSharedPreferences("Terminal_access", "true")
                updateSalesOverlayVisibility()

            } else {
                saveToSharedPreferences("Terminal_access", "false")
                updateSalesOverlayVisibility()
                setupUnpairedDeviceUI(terminalBtn, terminalStatus, terminalInfo)
                terminalBtn.setOnClickListener {
                    startReLinkProcess(context, pedData)
                }
            }
        }
    }

    private fun setupUnpairedDeviceUI(
        terminalBtn: ImageButton,
        terminalStatus: TextView,
        terminalInfo: LinearLayout
    ) {
        terminalBtn.visibility = View.VISIBLE
        terminalBtn.background = getDrawable(R.drawable.link_icon)
        terminalStatus.setTextColor(getColor(R.color.red))
        terminalStatus.text = "Unpaired"
        terminalInfo.visibility = View.GONE
    }

    private fun setupPairedDeviceUI(
        terminalBtn: ImageButton,
        terminalStatus: TextView,
        terminalInfo: LinearLayout,
        pedData: PedStatusResponse?,
        terminalNameTextView: TextView
    ) {
        terminalStatus.setTextColor(getColor(R.color.lavender))
        terminalBtn.background = getDrawable(R.drawable.unlink_icon)
        terminalStatus.text = "Paired"
        terminalInfo.visibility = View.VISIBLE
        terminalNameTextView.text = pedData?.merchant_terminal_id

        terminalBtn.setOnClickListener {
            showUnlinkPaymentTerminalDialog(pedData)
        }
    }

    private fun showUnlinkPaymentTerminalDialog(pedData: PedStatusResponse?) {
        val dialogOverlay = findViewById<FrameLayout>(R.id.dialog_unlink_payment_terminal)
        val confirmButton = findViewById<Button>(R.id.confirm_unbind_button)
        val cancelButton = findViewById<Button>(R.id.cancel_unbind_button)
        val confirmButtonLoader = findViewById<ProgressBar>(R.id.confirm_unbind_button_loader)

        disableDialogButton(confirmButton, confirmButtonLoader)
        dialogOverlay.visibility = View.VISIBLE

        lifecycleScope.launch {
            val (isOpen, statusCode) = withContext(Dispatchers.IO) {
                checkAndDisplayBusinessDayStatus(this@HomeScreen)
            }

            if (isOpen) {
                // If business day is open => must close
                enableDialogButton(confirmButton, confirmButtonLoader)
                confirmButton.text = "Close Business Day"
                confirmButton.setOnClickListener {
                    handleCloseBusinessDay(
                        pedData,
                        dialogOverlay,
                        confirmButton,
                        confirmButtonLoader
                    )
                }
            } else {
                // If business day is not open => unbind directly (unless there's an in-progress closure)
                if (statusCode != -1) {
                    // In-progress closure or blocking status
                    disableDialogButton(confirmButton, confirmButtonLoader)
                } else {
                    enableDialogButton(confirmButton, confirmButtonLoader)
                    confirmButton.text = "Unbind"
                    confirmButton.setOnClickListener {
                        handleUnbindTerminal(
                            pedData,
                            dialogOverlay,
                            confirmButton,
                            confirmButtonLoader
                        )
                    }
                }
            }
        }

        cancelButton.setOnClickListener {
            dialogOverlay.visibility = View.GONE
        }
    }

    private fun handleCloseBusinessDay(
        pedData: PedStatusResponse?,
        dialogOverlay: FrameLayout,
        confirmButton: Button,
        confirmButtonLoader: ProgressBar
    ) {
        saveToSharedPreferences("Terminal_access", "false")
        updateSalesOverlayVisibility()

        confirmButton.isEnabled = false
        dialogOverlay.visibility = View.GONE
        showLoader()

        lifecycleScope.launch {
            if (pedData != null) {
                disableDialogButton(confirmButton, confirmButtonLoader)
                val closeResult = withContext(Dispatchers.IO) {
                    closeBusinessDay(
                        this@HomeScreen,
                        MERCHANT_BANK_ID,
                        "merchant_email",
                        getUserParentData()?.email!!,
                        "merchant_terminal_id",
                        pedData.merchant_terminal_id
                    )
                }

                if (closeResult) {
                    hideLoader()
                    confirmButton.isEnabled = true
                    lifecycleScope.launch {
                        val (isOpenNow, code) = withContext(Dispatchers.IO) {
                            checkAndDisplayBusinessDayStatus(this@HomeScreen)
                        }
                        if (!isOpenNow && code == -1) {
                            refreshTransactionList()
                        }
                    }
                    enableDialogButton(confirmButton, confirmButtonLoader)
                } else {
                    hideLoader()
                    Toast.makeText(
                        this@HomeScreen,
                        "Failed to close business day",
                        Toast.LENGTH_LONG
                    ).show()
                    confirmButton.isEnabled = true
                    saveToSharedPreferences("Terminal_access", "true")
                    updateSalesOverlayVisibility()

                    enableDialogButton(confirmButton, confirmButtonLoader)
                }
            }
        }
    }

    private fun handleUnbindTerminal(
        pedData: PedStatusResponse?,
        dialogOverlay: FrameLayout,
        confirmButton: Button,
        confirmButtonLoader: ProgressBar
    ) {
        saveToSharedPreferences("Terminal_access", "false")
        updateSalesOverlayVisibility()

        confirmButton.isEnabled = false
        dialogOverlay.visibility = View.GONE
        showLoader()

        lifecycleScope.launch {
            if (pedData != null) {
                disableDialogButton(confirmButton, confirmButtonLoader)
                val unbindSuccess = withContext(Dispatchers.IO) {
                    unbindPaymentTerminal(
                        this@HomeScreen,
                        MERCHANT_BANK_ID,
                        "merchant_email",
                        getUserParentData()?.email!!,
                        "merchant_terminal_id",
                        pedData.merchant_terminal_id
                    )
                }

                if (unbindSuccess) {
                    confirmButton.isEnabled = true
                    lifecycleScope.launch {
                        getAndStoreUserPEDDetails(this@HomeScreen)
                    }
                    hideLoader()
                    enableDialogButton(confirmButton, confirmButtonLoader)
                } else {
                    hideLoader()
                    Toast.makeText(
                        this@HomeScreen,
                        "Failed to unbind payment terminal",
                        Toast.LENGTH_LONG
                    ).show()
                    confirmButton.isEnabled = true
                    saveToSharedPreferences("Terminal_access", "true")
                    updateSalesOverlayVisibility()

                    enableDialogButton(confirmButton, confirmButtonLoader)
                }
            }
        }
    }

    private fun startTerminalLinkProcess(context: Context, userTerminalData: PaymentTerminal?) {
        updateUIWithMessage("Processing")
        showSDKStatus()

        lifecycleScope.launch {
            val authToken = fetchAuthToken(context) ?: return@launch
            val terminalId = userTerminalData?.terminalName ?: return@launch
            val (success, connectionToken) = fetchConnectionToken(
                this@HomeScreen,
                authToken,
                terminalId,
                getUserParentData()?.email!!,
                "LINK"
            )

            if (!success || connectionToken == null) {
                hideSDKStatus()
                return@launch
            }
            startSdkInitialization(connectionToken)
        }
    }

    private fun startReLinkProcess(context: Context, pedData: PedStatusResponse?) {
        updateUIWithMessage("Processing")
        showSDKStatus()

        lifecycleScope.launch {
            val authToken = fetchAuthToken(context) ?: return@launch
            if (pedData != null && pedData.merchant_terminal_id != null) {

                val (success, connectionToken) = fetchConnectionToken(
                    this@HomeScreen,
                    authToken,
                    pedData.merchant_terminal_id,
                    getUserParentData()?.email!!,
                    "RELINK"
                )
                if (!success || connectionToken == null) {
                    runOnUiThread {
                        updateUIWithMessage(
                            "Failed to bind device to payment terminal. " +
                                    "Please unbind the previous device and try again"
                        )
                        findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                        closeButton.visibility = View.VISIBLE
                    }
                    return@launch
                }
                startSdkInitialization(connectionToken)
            }
        }
    }

    private fun startSdkInitialization(connectionToken: String?) {
        updateUIWithMessage("Initializing Device")

        SdkUtils.startSDKMethod(
            merchantBankId = MERCHANT_BANK_ID,
            appLanguageCode = APP_LANGUAGE_CODE,
            solutionPartnerId = SOLUTION_PARTNER_ID,
            context = this,
            activityContext = this
        ) { startResponse ->
            val responseMessage = ResponseCodes.responseMessages[startResponse?.responseCode]

            when (startResponse?.responseCode) {
                "910" -> {
                    configureDeviceSetup(connectionToken)
                }
                "0" -> {
                    hideSDKStatus()
                }
                else -> {
                    runOnUiThread {
                        updateUIWithMessage(
                            "Initialization failed: ${
                                responseMessage ?: "Unknown error, Code: ${startResponse?.description}"
                            }"
                        )
                        findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                        closeButton.visibility = View.VISIBLE
                    }
                }
            }
        }
    }

    private fun configureDeviceSetup(connectionToken: String?) {
        updateUIWithMessage("Configuring Device to Accept Payment")

        SdkUtils.deviceSetupSDKMethod(
            merchantBankId = MERCHANT_BANK_ID,
            appLanguageCode = APP_LANGUAGE_CODE,
            solutionPartnerId = SOLUTION_PARTNER_ID,
            connectionToken = connectionToken ?: "",
            context = this
        ) { setupResponse ->
            val setupResponseMessage = ResponseCodes.responseMessages[setupResponse?.responseCode]
            runOnUiThread {
                if (setupResponse?.responseCode == "909") {
                    saveToSharedPreferences("Terminal_access", "true")
                    updateSalesOverlayVisibility()

                    updateUIWithMessage("Almost Done")

                    SdkUtils.startSDKMethod(
                        merchantBankId = MERCHANT_BANK_ID,
                        appLanguageCode = APP_LANGUAGE_CODE,
                        solutionPartnerId = SOLUTION_PARTNER_ID,
                        context = this,
                        activityContext = this
                    ) {
                        hideSDKStatus()
                    }

                    lifecycleScope.launch {
                        getAndStoreUserPEDDetails(this@HomeScreen)
                    }
                } else {
                    updateUIWithMessage(
                        "Device Configuration failed: ${
                            setupResponseMessage ?: "Unknown error, Code: ${setupResponse?.description}"
                        }"
                    )
                    closeButton.visibility = View.VISIBLE
                    findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                }
            }
        }
    }

    private fun showLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.VISIBLE
    }

    private fun hideLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.GONE
    }

    private fun updateUIWithMessage(message: String) {
        runOnUiThread {
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.VISIBLE
            findViewById<TextView>(R.id.sdk_status_message).text = message
        }
    }

    private fun showSDKStatus() {
        runOnUiThread {
            findViewById<View>(R.id.sdk_status_overlay).visibility = View.VISIBLE
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.VISIBLE
            closeButton.visibility = View.GONE
        }
    }

    private fun hideSDKStatus() {
        runOnUiThread {
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
            findViewById<View>(R.id.sdk_status_overlay).visibility = View.GONE
            closeButton.visibility = View.GONE
        }
    }

    private fun disableDialogButton(button: Button, loader: ProgressBar) {
        button.isEnabled = false
        button.text = ""
        button.setBackgroundColor(ContextCompat.getColor(this@HomeScreen, R.color.gray))
        loader.visibility = View.VISIBLE
    }

    private fun enableDialogButton(button: Button, loader: ProgressBar) {
        button.isEnabled = true
        button.setBackgroundColor(ContextCompat.getColor(this@HomeScreen, R.color.lavender))
        loader.visibility = View.GONE
    }

    private fun refreshTransactionList() {
        if (!isSessionValid(this)) {
            handleSessionExpiry(this)
        } else {
            currentPage = 1
            hasMoreData = true
            isLoading = false

            transactionList.clear()
            transactionAdapter.notifyDataSetChanged()

            fetchTransactionData()
        }
    }
}