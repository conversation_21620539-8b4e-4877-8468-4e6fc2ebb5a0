package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.graphics.Typeface
import android.os.Bundle
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import com.hazelpay.merchant.tap2pay.utils.EnableReaderMode
import com.hazelpay.merchant.tap2pay.utils.checkAndUpdateApp
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.handleSessionExpiry
import com.hazelpay.merchant.tap2pay.utils.isSessionValid
import com.hazelpay.merchant.tap2pay.utils.openPlayStore
import java.lang.reflect.Field

class SplashScreen : AppCompatActivity() {

    private var isNfcChecked = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_splash_screen)

        overrideDefaultFont("DEFAULT", R.font.helvetica);
        overrideDefaultFont("MONOSPACE", R.font.helvetica);
        overrideDefaultFont("SERIF", R.font.helvetica);
        overrideDefaultFont("SANS_SERIF", R.font.helvetica);
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);

        enforceOrientation(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        if (isSessionValid(this)) {
            EnableReaderMode.enableReaderMode(this, this) {
                isNfcChecked = true
                proceedToNextScreen()
            }
        }

        findViewById<LinearLayout>(R.id.ll_continue_btn).setOnClickListener{
            if (!isNfcChecked) {
                checkAndUpdateApp(this) { success ->
                    if (!success) {
                        openPlayStore(this)
                    }
                }
                EnableReaderMode.enableReaderMode(this, this) {
                    isNfcChecked = true
                    proceedToNextScreen()
                }
            }
        }
    }
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun proceedToNextScreen() {
        if (!isSessionValid(this)) {
            handleSessionExpiry(this)
            finish()
        } else{
            startActivity(Intent(this, HomeScreen::class.java))
            finish()
        }
    }

    private fun overrideDefaultFont(defaultFontNameToOverride: String, fontResourcePath: Int) {
        try {
            val customFontTypeface: Typeface = resources.getFont(fontResourcePath)
            val defaultFontTypefaceField: Field =
                Typeface::class.java.getDeclaredField(defaultFontNameToOverride)
            defaultFontTypefaceField.isAccessible = true
            defaultFontTypefaceField.set(null, customFontTypeface)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
