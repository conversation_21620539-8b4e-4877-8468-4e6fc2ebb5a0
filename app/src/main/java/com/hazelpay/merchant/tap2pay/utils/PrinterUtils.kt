package com.hazelpay.merchant.tap2pay.utils

import android.content.Context
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintJob
import android.print.PrintManager
import android.webkit.WebView
import android.webkit.WebViewClient
import com.hazelpay.merchant.tap2pay.model.ReceiptData

object PrinterUtils {

    /**
     * Print receipt using Android's standard print preview
     */
    fun printReceipt(
        context: Context,
        receiptData: ReceiptData,
        callback: (Boolean, String?) -> Unit
    ) {
        try {
            // Create HTML content for the receipt
            val htmlContent = generateReceiptHtml(receiptData)

            // Create WebView to render the HTML
            val webView = WebView(context)
            webView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)

                    // Create print job
                    createPrintJob(context, webView, callback)
                }
            }

            // Load HTML content
            webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)

        } catch (e: Exception) {
            callback(false, "Failed to create print preview: ${e.message}")
        }
    }

    private fun createPrintJob(context: Context, webView: WebView, callback: (Boolean, String?) -> Unit) {
        try {
            val printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager

            val printAdapter: PrintDocumentAdapter = webView.createPrintDocumentAdapter("Receipt")

            // Create custom media size for 80mm thermal paper (80mm width, variable height)
            // Using higher resolution values for better preview quality
            val thermalPaperSize = PrintAttributes.MediaSize("thermal_80mm", "80mm Thermal Paper", 3150, 7000)

            val printAttributes = PrintAttributes.Builder()
                .setMediaSize(thermalPaperSize)
                .setResolution(PrintAttributes.Resolution("thermal", "Thermal", 300, 300))
                .setColorMode(PrintAttributes.COLOR_MODE_MONOCHROME)
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()

            val printJob: PrintJob = printManager.print(
                "Receipt_${System.currentTimeMillis()}",
                printAdapter,
                printAttributes
            )

            callback(true, "Print preview opened successfully")

        } catch (e: Exception) {
            callback(false, "Failed to open print preview: ${e.message}")
        }
    }

    private fun generateReceiptHtml(receiptData: ReceiptData): String {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    @page {
                        size: 80mm auto;
                        margin: 0;
                        -webkit-print-color-adjust: exact;
                    }
                    body {
                        font-family: 'Courier New', monospace;
                        font-size: 20px;
                        margin: 0;
                        padding: 5mm 8mm;
                        width: 100%;
                        box-sizing: border-box;
                        background: white;
                        -webkit-print-color-adjust: exact;
                    }
                    .content {
                        padding: 5mm 5mm;
                        width: 100%;
                    }
                    .center {
                        text-align: center;
                    }
                    .separator {
                        border-top: 3px dashed #000;
                        margin: 12px 0;
                        width: 100%;
                    }
                    .total {
                        font-weight: bold;
                        font-size: 28px;
                        text-align: center;
                        margin: 10px 0;
                    }
                    .status {
                        font-weight: bold;
                        text-align: center;
                        margin: 12px 0;
                        font-size: 24px;
                    }
                    .header-line {
                        text-align: center;
                        margin: 4px 0;
                        font-size: 22px;
                    }
                    .detail-line {
                        margin: 5px 0;
                        word-wrap: break-word;
                        font-size: 20px;
                    }
                    .company-name {
                        font-weight: bold;
                        font-size: 26px;
                    }
                    .footer-text {
                        font-size: 18px;
                        margin: 3px 0;
                        text-align: center;
                    }
                </style>
            </head>
            <body>
                <div class="center">
                    <div class="header-line">================================</div>
                    <div class="header-line company-name">${receiptData.companyName}</div>
                    <div class="header-line">ID: ${receiptData.companyId}</div>
                    <div class="header-line">${receiptData.address}</div>
                    <div class="header-line">================================</div>
                </div>

                <br>

                ${if (!receiptData.vatPercentage.isNullOrEmpty() && receiptData.vatPercentage != "0%")
                    "<div class=\"detail-line\">VAT (${receiptData.vatPercentage}): ${receiptData.currency} ${receiptData.vatAmount}</div>"
                else ""}

                ${if (!receiptData.tipPercentage.isNullOrEmpty() && receiptData.tipPercentage != "0%")
                    "<div class=\"detail-line\">Tip (${receiptData.tipPercentage}): ${receiptData.currency} ${receiptData.tipAmount}</div>"
                else ""}

                <div class="detail-line">Subtotal: ${receiptData.currency} ${receiptData.subtotal}</div>

                <div class="separator"></div>

                <div class="total">TOTAL: ${receiptData.currency} ${receiptData.total}</div>

                <div class="status">*** ${receiptData.status} ***</div>

                <br>

                <div class="detail-line">Date/Time: ${receiptData.dateTime}</div>
                <div class="detail-line">Card: ${receiptData.cardMask}</div>
                <div class="detail-line">Payment Method: ${receiptData.paymentMethod}</div>
                <div class="detail-line">TID: ${receiptData.transactionId}</div>
                <div class="detail-line">MID: ${receiptData.companyId}</div>
                <div class="detail-line">RRN: ${receiptData.rrn}</div>
                <div class="detail-line">Auth Code: ${receiptData.authCode}</div>
                <div class="detail-line">AID: ${receiptData.aid}</div>

                ${if (!receiptData.tvr.isNullOrEmpty()) "<div class=\"detail-line\">TVR: ${receiptData.tvr}</div>" else ""}

                <br>

                <div class="center">
                    <div class="header-line">================================</div>
                    <div class="header-line">Thank you for your business!</div>
                    <div class="footer-text">Processed by HazelOne</div>
                    <div class="footer-text">(a Product of EftaaPay)</div>
                    <div class="header-line">================================</div>
                </div>
            </body>
            </html>
        """.trimIndent()
    }
}
