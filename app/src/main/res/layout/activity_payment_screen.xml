<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/platinum"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.05" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.95" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.98" />


    <TextView
        android:id="@+id/payment_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/payment_title"
        android:textStyle="bold"
        android:textSize="20sp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.498"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toTopOf="@id/guideline_horizontal_top" />


    <TextView
        android:id="@+id/screen_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:text="@string/screen_title"
        android:textColor="@color/lite_gray"
        android:textSize="15sp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/payment_title" />

    <TextView
        android:id="@+id/enter_amount_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/enter_amount_title"

        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/screen_title" />


    <EditText
        android:id="@+id/amount_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginTop="16dp"
        android:hint="Amount"
        android:inputType="numberDecimal"
        android:textSize="17sp"
        android:background="@android:color/transparent"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/enter_amount_title" />


    <View
        android:id="@+id/divider1"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/gray"
        app:layout_constraintTop_toBottomOf="@id/amount_value"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right" />


    <TextView
        android:id="@+id/reference_id_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/reference_id_text"

        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/divider1" />


    <EditText
        android:id="@+id/reference_id_value"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginTop="12dp"
        android:background="@android:color/transparent"
        android:hint="Reference ID"

        android:textSize="17sp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/reference_id_text" />


    <View
        android:id="@+id/divider2"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/gray"
        app:layout_constraintTop_toBottomOf="@id/reference_id_value"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right" />


    <!-- The text view remains unchanged -->
    <TextView
        android:id="@+id/select_payment_method_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="36dp"
        android:text="@string/select_payment_method_text"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/divider2" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/payment_methods_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/select_payment_method_text"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right">

        <ImageButton
            android:id="@+id/payment_method_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:background="@android:color/transparent"
            android:src="@drawable/card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/qr_payment_method_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@android:color/transparent"
            android:src="@drawable/qr_coming_soon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.055"
            app:layout_constraintStart_toEndOf="@id/payment_method_icon"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageButton
        android:id="@+id/next_button"
        android:layout_width="201dp"
        android:layout_height="59dp"
        android:layout_marginStart="32dp"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="140dp"
        android:background="@drawable/button_outline_green"
        android:padding="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/payment_methods_container"
        app:layout_constraintVertical_bias="0.3" />

    <TextView
        android:id="@+id/next_button_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/next_button_text"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/next_button"
        app:layout_constraintEnd_toEndOf="@+id/next_button"
        app:layout_constraintStart_toStartOf="@+id/next_button"
        app:layout_constraintTop_toTopOf="@+id/next_button" />


    <androidx.cardview.widget.CardView
        android:id="@+id/navbarcardView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="8dp"
        app:cardCornerRadius="10dp"
        app:cardBackgroundColor="@color/card_background"
        app:layout_constraintBottom_toBottomOf="@id/guideline_horizontal_bottom"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right">

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/navbar_background"
            app:itemIconSize="30sp"
            app:itemIconTint="@color/navbar_icon_color"
            app:itemTextColor="@color/navbar_text_color"
            app:menu="@menu/bottom_nav_menu"
            app:labelVisibilityMode="labeled" />
    </androidx.cardview.widget.CardView>

    <include layout="@layout/loader_overlay" />
    <include layout="@layout/sdk_status_overlay" />
</androidx.constraintlayout.widget.ConstraintLayout>
