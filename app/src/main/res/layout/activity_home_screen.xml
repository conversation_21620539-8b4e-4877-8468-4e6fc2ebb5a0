<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/platinum"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.96" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.03" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.98" />

    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10sp"
        android:orientation="horizontal"
        android:padding="10dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toTopOf="@id/guideline_horizontal_top">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_username_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/black"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>

        <ImageView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginEnd="8dp"
            android:scaleType="fitCenter"
            android:src="@drawable/merchantapplogonobg" />
    </LinearLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/navbarcardView"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/header_layout">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            app:layout_constraintTop_toBottomOf="@id/header_layout">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toTopOf="@id/guideline_horizontal_transactions"
                app:layout_constraintTop_toBottomOf="@id/header_layout">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_vertical_left1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_begin="0dp" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_vertical_right1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="1.0" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_horizontal_transactions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintGuide_percent="0.99" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardView1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25sp"
                    android:layout_marginEnd="25sp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:layout_constraintEnd_toEndOf="@+id/guideline_vertical_right1"
                    app:layout_constraintStart_toStartOf="@id/guideline_vertical_left1"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="vertical"
                        android:padding="15dp">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/txt_connection_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/connection_status"
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/txt_connection_status_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingStart="5dp"
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="@id/txt_connection_status"
                                app:layout_constraintStart_toEndOf="@id/txt_connection_status"
                                app:layout_constraintTop_toTopOf="@id/txt_connection_status" />

                            <ImageButton
                                android:id="@+id/btn_terminal"
                                android:layout_width="26sp"
                                android:layout_height="26sp"
                                android:background="?attr/selectableItemBackgroundBorderless"
                                android:scaleType="centerInside"
                                android:src="@drawable/link_icon"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <LinearLayout
                            android:id="@+id/terminal_info"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:visibility="gone"
                            app:layout_constraintStart_toStartOf="@+id/txt_connection_status"
                            app:layout_constraintTop_toBottomOf="@+id/txt_connection_status">

                            <TextView
                                android:id="@+id/txt_terminal_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/terminal_name"
                                android:textColor="@color/black"
                                android:textSize="11sp"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/txt_terminal_name_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black"
                                android:textSize="11sp"
                                android:visibility="visible" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/txt_business_day_status"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:visibility="gone" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardView2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="1sp"
                    android:layout_marginTop="10sp"
                    android:layout_marginEnd="1sp"
                    android:background="@color/white"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right1"
                    app:layout_constraintStart_toStartOf="@id/guideline_vertical_left1"
                    app:layout_constraintTop_toBottomOf="@id/cardView1">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="start"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <TextView
                            android:id="@+id/txt_sale_today"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/income_button_text"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/income_value_main"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/lavender"
                                android:textSize="40sp"
                                android:textStyle="bold" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/txt_currency_home"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end"
                                    android:layout_marginStart="6sp"
                                    android:layout_marginTop="5sp"
                                    android:textColor="@color/gray"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/income_value_decimal"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="bottom"
                                    android:paddingBottom="5dp"
                                    android:textColor="@color/lavender"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <TextView
                    android:id="@+id/transaction_list_header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="10sp"
                    android:text="@string/transaction_list_header"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right1"
                    app:layout_constraintStart_toStartOf="@id/guideline_vertical_left1"
                    app:layout_constraintTop_toBottomOf="@id/cardView2" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="1sp"
                    android:layout_marginEnd="1sp"
                    android:layout_marginBottom="5dp"
                    app:layout_constraintBottom_toTopOf="@id/guideline_horizontal_transactions"
                    app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right1"
                    app:layout_constraintStart_toStartOf="@id/guideline_vertical_left1"
                    app:layout_constraintTop_toBottomOf="@id/transaction_list_header">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/transaction_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipToPadding="false"
                        android:nestedScrollingEnabled="true"
                        android:overScrollMode="always"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:listitem="@layout/item_transaction" />

                    <LinearLayout
                        android:id="@+id/empty_state_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/empty_state_image"
                            android:layout_width="80dp"
                            android:layout_height="80dp"
                            android:layout_gravity="center_horizontal"
                            android:src="@drawable/ic_empty_state" />

                        <TextView
                            android:id="@+id/empty_state_text"
                            android:layout_marginTop="5sp"
                            android:layout_marginStart="5sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:text="@string/no_transactions" />
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/navbarcardView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="8dp"
        app:cardBackgroundColor="@color/card_background"
        app:cardCornerRadius="10dp"
        app:layout_constraintBottom_toBottomOf="@id/guideline_horizontal_bottom"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left">

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/navbar_background"
            app:itemIconSize="30sp"
            app:itemIconTint="@color/navbar_icon_color"
            app:itemTextColor="@color/navbar_text_color"
            app:labelVisibilityMode="labeled"
            app:menu="@menu/bottom_nav_menu" />
    </androidx.cardview.widget.CardView>

    <include layout="@layout/loader_overlay" />

    <include layout="@layout/sdk_status_overlay" />

    <include layout="@layout/dialog_unlink_payment_terminal" />
</androidx.constraintlayout.widget.ConstraintLayout>