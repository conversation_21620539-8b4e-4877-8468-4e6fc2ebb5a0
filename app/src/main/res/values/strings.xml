<resources>
    <string name="app_name">HazelsOne</string>
    <string name="title_home">Home</string>
    <string name="title_dashboard">Dashboard</string>
    <string name="title_notifications">Notifications</string>
    <string name="first_fragment_label">First Fragment</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>
    <string name="phone_number_title">Phone Number</string>
    <string name="phone_number_subtitle">Please add the customer phone number to send the payment receipt</string>

    <string name="encounterissue">Encounter an issue?</string>

    <string name="termsofuse">Terms of Use</string>

    <string name="logout">Logout</string>


    <string name="confirm_button_text">Confirm</string>


    <string name="phone_tag">Phone Number</string>

    <string name="email_title">Email Address</string>
    <string name="email_subtitle">Please add the customer email address to send the payment receipt</string>
    <string name="email_hint">Email Address</string>

    <string name="email_tag">Email Address</string>

    <string name="phone_number_sub">Please add your mobile phone number</string>


    <string name="title_pin_verification">Pin Verification</string>
    <string name="subtitle_pin_verification">Please enter your card pin to continue</string>
    <string name="verify_button_text">Verify</string>
    <string name="delete_button_content_description">Back</string>
    <string name="keypad_1">1</string>
    <string name="keypad_2">2</string>
    <string name="keypad_3">3</string>
    <string name="keypad_4">4</string>
    <string name="keypad_5">5</string>
    <string name="keypad_6">6</string>
    <string name="keypad_7">7</string>
    <string name="keypad_8">8</string>
    <string name="keypad_9">9</string>
    <string name="keypad_0">0</string>

    <string name="pin_verification_title">Pin Verification</string>
    <string name="pin_verification_subtitle">Please enter your card pin to continue</string>

    <string name="change_password_title">Change Password</string>
    <string name="old_password_label">Old Password</string>
    <string name="old_password_hint">Old Password</string>
    <string name="password_label">New Password</string>
    <string name="password_hint">Password</string>
    <string name="confirm_password_label">Confirm Password</string>
    <string name="confirm_password_hint">Confirm Password</string>
    <string name="save_button_text">Save</string>
    <string name="change_pin_title">Change Pin</string>
    <string name="change_pin_subtitle">Please enter your new pin to continue</string>


    <string name="home_title">Home</string>
    <string name="close_register_button_text">Close Register</string>
    <string name="open_register_button_text">Open Register</string>

    <string name="income_button_text">Sale Today</string>
    <string name="today_date_sale">Today</string>

    <string name="connection_status">Terminal Status:&#160;</string>
    <string name="terminal_name">Terminal Name:&#160;</string>

    <string name="terminal_connected">Paired</string>
    <string name="terminal_not_connected">Unpaired</string>


    <string name="income_amount_text">0</string>
    <string name="transaction_button_text">Transactions</string>
    <string name="transaction_amount_text">0</string>


    <string name="login_title">Log in</string>
    <string name="email_address_tag">Email Address</string>

    <string name="password_tag">Password</string>
    <string name="login_button_text">Login</string>
    <string name="already_have_account_text">Don’t have an account?</string>
    <string name="signup_text">Sign up</string>


    <string name="welcome_text">Welcome to</string>
    <string name="tagline_text">Your Best Payment Gateway</string>
    <string name="get_started_button_text">Get Started</string>


    <string name="payment_receipt_title">Payment Receipt</string>
    <string name="transaction_id">Transaction ID</string>
    <string name="transaction_id_value">2313123</string>
    <string name="currency_text">Currency</string>
    <string name="currency_value">EUR</string>
    <string name="total_amount_text">Total amount</string>
    <string name="total_amount_value">$55</string>
    <string name="payment_date_text">Date</string>
    <string name="payment_date_value">July 03, 2024, 16:30</string>
    <string name="payment_method_text">Payment</string>
    <string name="payment_method_value">VISA</string>
    <string name="card_details_text">Card Details</string>
    <string name="card_details_value">XXXX XXXX XXXX 1111</string>
    <string name="back_home_button_text">Back to Home</string>

    <string name="payment_title">Payment</string>
    <string name="screen_title">Please enter the money transfer amount in the below field.</string>
    <string name="enter_amount_title">Enter Amount</string>
    <string name="reference_id_text">Reference ID (Optional)</string>
    <string name="select_payment_method_text">Select Payment Method</string>
    <string name="next_button_text">Pay</string>


    <string name="title">Set Administrator Pin Code</string>
    <string name="subtitle">Please set your administrator pin to use refund functionality in the application</string>
    <string name="set_button_text">Set</string>


    <string name="profile_title">Profile</string>
    <string name="profile_subtitle">Please set up your profile</string>
    <string name="first_name_hint">First Name</string>
    <string name="last_name_hint">Last Name</string>

    <string name="pin_verification">Please enter your administrator pin to continue</string>

    <string name="email_sub">Please add the customer email address to send the payment receipt</string>

    <string name="failed_title">Transaction Failed</string>
    <string name="failure_subtitle">Your transaction has failed</string>
    <string name="back_home_btn_text">Back to Home</string>

    <string name="refund_reports_title">Refund Reports</string>

    <string name="vat">VAT %</string>
    <string name="total_amount">Total amount</string>
    <string name="date">Date</string>
    <string name="payment_method">Payment</string>
    <string name="card_details">Card Details</string>
    <string name="back_to_home">Back to Home</string>

    <string name="report_title">Reports</string>

    <string name="income">INCOME</string>
    <string name="transactions">Transactions</string>
    <string name="today">Today</string>

    <string name="refund_title">Refund</string>
    <string name="refund_instruction">Please enter the refund amount below</string>
    <string name="enter_amount">Enter Amount</string>
    <string name="amount_hint">Amount</string>
    <string name="reference_id">Reference ID (Optional)</string>
    <string name="reference_id_hint">Reference ID</string>
    <string name="select_payment_method">Select Payment Method</string>
    <string name="signature_verification">Signature Verification</string>
    <string name="clear_button_text">Clear</string>
    <string name="proceed_button_text">Proceed</string>
    <string name="congrats_text">Congrats!</string>
    <string name="refund_successful_text">Refund Successful</string>
    <string name="transaction_summary_text">Detailed transaction summary is available using the methods below</string>


    <string name="date_label">Date Range</string>
    <string name="month_july">JULY</string>
    <string name="income_label">INCOME</string>
    <string name="income_value">0.00</string>
    <string name="transactions_label">Transactions</string>
    <string name="transactions_value">0</string>
    <string name="reports_label">Settled Transactions</string>

    <string name="security_question_title">Security Questions</string>
    <string name="security_question">" What was your First School's Name?"</string>
    <string name="security_question_subtitle">Please, write a short answer in the field below</string>
    <string name="security_answer_hint">Write your answer here...</string>


    <string name="personal_info_title">Personal Information</string>
    <string name="name_label">Name</string>
    <string name="email_label">Email</string>
    <string name="mobile_phone_label">Mobile Phone</string>
    <string name="address_label">Address</string>
    <string name="terminal_info_title">Terminal Information</string>
    <string name="device_label">Device ID</string>
    <string name="terminal_label">Terminal Name</string>
    <string name="terminal_id_label">Terminal ID</string>
    <string name="security_title">Security</string>
    <string name="change_pin_label">Change Admin Pin</string>
    <string name="change_password_label">Change Password</string>


    <string name="signature_verification_title">Signature Verification</string>

    <string name="sign_up_title">Sign Up</string>

    <string name="confirm_password_tag">Confirm Password</string>

    <string name="sign_up_button">Sign Up</string>
    <string name="already_have_account">Already have an account?</string>
    <string name="login">Login</string>

    <string name="description_text">Your Best Payment Gateway</string>
    <string name="splash_text">Secured by EftaaPay</string>

    <string name="back_button">Back</string>
    <string name="tap_to_pay_title">Tap to Pay</string>
    <string name="transaction_for_amount">Transaction for amount:</string>
    <string name="amount_value">$56,000</string>
    <string name="tap_to_pay_message">Tap to Pay</string>
    <string name="tap_to_pay_submessage">Please align your mobile phone to the payment terminal</string>
    <string name="cancel_button">Cancel</string>


    <string name="tap_to_refund_title">Tap to Refund</string>


    <string name="tap_to_refund">Tap to Refund</string>
    <string name="align_mobile_message">Please align your mobile phone to the payment terminal</string>

    <string name="failure_icon_description">Failure Icon</string>
    <string name="transaction_failed">Transaction Failed</string>
    <string name="transaction_failure_message">Your transaction could not be completed due to an unexpected error. Please contact our support team for assistance.</string>

    <string name="congrats">Congrats!</string>
    <string name="payment_success_message">Payment Successful</string>
    <string name="detailed_summary_message">Detailed transaction summary is available using the methods below</string>
    <string name="sale">Sale</string>
    <string name="online_transaction">Online Transaction</string>
    <string name="amount_200">$200</string>
    <string name="transaction_name">Transaction Name</string>
    <string name="transaction_date">Transaction Date</string>
    <string name="transaction_amount_item">$0.00</string>


    <string name="enter_email_address">Please enter an email address</string>
    <string name="email_confirmed">Email confirmed: %1$s</string>
    <string name="valid_phone_number_error">Please enter a valid phone number</string>

    <string name="pin_verified_successfully">Pin code verified successfully</string>
    <string name="valid_pin_error">Please enter a valid 5-digit pin</string>
    <string name="pin_entered_successfully">Pin code entered successfully</string>
    <string name="enter_old_password">Please enter your old password</string>
    <string name="invalid_password_message">Password must be at least 8 characters long, with a mix of upper case; lowercase letters, numbers, and special characters.</string>
    <string name="passwords_do_not_match">Passwords do not match!</string>
    <string name="password_changed_success">Password changed successfully!</string>
    <string name="pin_changed_success">Pin code changed successfully</string>
    <string name="invalid_pin_message">Please enter a valid 5-digit pin</string>


    <string name="register_opened">Register Opened</string>
    <string name="register_closed">Register Closed</string>


    <string name="cancel">Cancel</string>
    <string name="refund">Refund</string>


    <string name="error_empty_email">Email cannot be empty</string>
    <string name="error_invalid_email">Please enter a valid email address</string>
    <string name="error_empty_password">Password cannot be empty</string>
    <string name="error_invalid_password">Password must be at least 8 characters, include upper and lower case letters, a number, and a special character.</string>
    <string name="error_missing_credentials">Please enter your email and password</string>


    <string name="password_visible">Show Password</string>
    <string name="password_hidden">Hide Password</string>

    <string name="storage_permission_title">Storage Permission Needed</string>
    <string name="storage_permission_message">This permission is needed to save the receipt as an image file.</string>
    <string name="ok">OK</string>

    <string name="permission_denied_message">Permission denied. Cannot save the image.</string>
    <string name="screenshot_saved">Screenshot saved to Pictures/Receipts</string>
    <string name="screenshot_saved_to_pictures">Screenshot saved to Pictures</string>
    <string name="screenshot_error">Error saving screenshot</string>

    <string name="enter_valid_amount">Please enter a valid amount</string>
    <string name="amount_positive_number">Amount must be a positive number</string>

    <string name="proceeding_with_payment">Proceeding with Amount: %1$s, Reference: %2$s</string>
    <string name="pin_code_set_success">Pin code set successfully</string>
    <string name="enter_valid_pin">Please enter a valid 5-digit pin</string>
    <string name="fill_name_message">Please fill in both first and last name</string>
    <string name="pin_entered_success">Pin code entered successfully</string>
    <string name="enter_valid_phone_number">Please enter a valid phone number</string>


    <string name="screenshot_saved_scoped">Screenshot saved to Pictures/Receipts</string>
    <string name="screenshot_saved_legacy">Screenshot saved to Pictures</string>

    <string name="positive_number_required">Amount must be a positive number</string>

    <string name="proceeding_with_amount">Proceeding with Amount: %1$s, Reference: %2$s</string>

    <string name="signature_started">Signature started</string>
    <string name="signature_cleared">Signature cleared</string>
    <string name="provide_signature">Please provide a signature.</string>
    <string name="congrats_message">Congrats!</string>

    <string name="sending_email">Sending email...</string>
    <string name="sending_sms">Sending SMS...</string>
    <string name="downloading_receipt">Downloading receipt...</string>

    <string name="transaction_shopping">Shopping</string>
    <string name="transaction_food_delivery">Food Delivery</string>
    <string name="transaction_refund">Refund</string>

    <string name="please_provide_answer">Please provide an answer.</string>

    <string name="unsaved_changes_message">You have unsaved changes. Are you sure you want to leave?</string>
    <string name="discard">Discard</string>

    <string name="email_cannot_be_empty">Email cannot be empty</string>
    <string name="invalid_email_address">Please enter a valid email address</string>
    <string name="password_cannot_be_empty">Password cannot be empty</string>
    <string name="confirm_password_empty">Confirm password cannot be empty</string>
    <string name="invalid_password_format">Password must be at least 8 characters, include upper and lower case letters, a number, and a special character.</string>
    <string name="transaction_cancelled">Transaction cancelled</string>

    <string name="payment_successful">Payment Successful</string>
    <string name="dark">Dark Mode</string>
    <string name="biometric">Enable Biometric Login</string>
    <string name="biometric_not_available">Biometric not available on your device</string>
    <string name="biometric_enabled">Biometric authentication enabled</string>
    <string name="biometric_disabled">Biometric authentication disabled</string>

    <string name="error_email_empty">Email address is required.</string>


    <string name="error_decode_token">Failed to decode token.</string>
    <string name="error_login_failed">Login failed, please check your credentials and try again.</string>

    <string name="login_success">Login successful!</string>
    <string name="admin_not_allowed">Admin accounts are not allowed to login, Please use a terminal account</string>
    <string name="error_server_unreachable">Cannot reach server, please try again later.</string>


    <string name="register_closed_message">Register is closed. Please open it first.</string>
    <string name="transaction_sale">Sale</string>

    <string name="transaction_cancel">Cancel</string>
    <string name="transaction_online">Online transaction</string>
    <string name="open_register_to_make_transactions">Please pair payment terminal to make transactions.</string>
    <string name="failed_fetch_business_day_status">Failed to fetch business day status.</string>
    <string name="register_closed_successfully">Register closed successfully.</string>
    <string name="failed_to_close_register">Failed to close the register.</string>
    <string name="error_generic">An error occurred</string>
    <string name="session_expired_message">Session expired. Please log in again.</string>
    <string name="open_register">Open Register</string>
    <string name="close_register">Close Register</string>


    <string name="settings_title">Settings</string>
    <string name="log_out_title">Log Out</string>
    <string name="log_out_message">Are you sure you want to log out?</string>
    <string name="log_out_positive">Yes</string>
    <string name="log_out_negative">No</string>
    <string name="logged_out_success">Logged out successfully.</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="change_pin">Change PIN</string>
    <string name="change_password">Change Password</string>

    <string name="invalid_phone_number">Invalid phone number. Please enter a valid phone number with a country code.</string>
    <string name="confirm_button">Confirm</string>
    <string name="phone_number_hint">Enter phone number</string>


    <string name="lorem_ipsum">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam in scelerisque sem. Mauris
        volutpat, dolor id interdum ullamcorper, risus dolor egestas lectus, sit amet mattis purus
        dui nec risus. Maecenas non sodales nisi, vel dictum dolor. Class aptent taciti sociosqu ad
        litora torquent per conubia nostra, per inceptos himenaeos. Suspendisse blandit eleifend
        diam, vel rutrum tellus vulputate quis. Aliquam eget libero aliquet, imperdiet nisl a,
        ornare ex. Sed rhoncus est ut libero porta lobortis. Fusce in dictum tellus.\n\n
        Suspendisse interdum ornare ante. Aliquam nec cursus lorem. Morbi id magna felis. Vivamus
        egestas, est a condimentum egestas, turpis nisl iaculis ipsum, in dictum tellus dolor sed
        neque. Morbi tellus erat, dapibus ut sem a, iaculis tincidunt dui. Interdum et malesuada
        fames ac ante ipsum primis in faucibus. Curabitur et eros porttitor, ultricies urna vitae,
        molestie nibh. Phasellus at commodo eros, non aliquet metus. Sed maximus nisl nec dolor
        bibendum, vel congue leo egestas.\n\n
        Sed interdum tortor nibh, in sagittis risus mollis quis. Curabitur mi odio, condimentum sit
        amet auctor at, mollis non turpis. Nullam pretium libero vestibulum, finibus orci vel,
        molestie quam. Fusce blandit tincidunt nulla, quis sollicitudin libero facilisis et. Integer
        interdum nunc ligula, et fermentum metus hendrerit id. Vestibulum lectus felis, dictum at
        lacinia sit amet, tristique id quam. Cras eu consequat dui. Suspendisse sodales nunc ligula,
        in lobortis sem porta sed. Integer id ultrices magna, in luctus elit. Sed a pellentesque
        est.\n\n
        Aenean nunc velit, lacinia sed dolor sed, ultrices viverra nulla. Etiam a venenatis nibh.
        Morbi laoreet, tortor sed facilisis varius, nibh orci rhoncus nulla, id elementum leo dui
        non lorem. Nam mollis ipsum quis auctor varius. Quisque elementum eu libero sed commodo. In
        eros nisl, imperdiet vel imperdiet et, scelerisque a mauris. Pellentesque varius ex nunc,
        quis imperdiet eros placerat ac. Duis finibus orci et est auctor tincidunt. Sed non viverra
        ipsum. Nunc quis augue egestas, cursus lorem at, molestie sem. Morbi a consectetur ipsum, a
        placerat diam. Etiam vulputate dignissim convallis. Integer faucibus mauris sit amet finibus
        convallis.\n\n
        Phasellus in aliquet mi. Pellentesque habitant morbi tristique senectus et netus et
        malesuada fames ac turpis egestas. In volutpat arcu ut felis sagittis, in finibus massa
        gravida. Pellentesque id tellus orci. Integer dictum, lorem sed efficitur ullamcorper,
        libero justo consectetur ipsum, in mollis nisl ex sed nisl. Donec maximus ullamcorper
        sodales. Praesent bibendum rhoncus tellus nec feugiat. In a ornare nulla. Donec rhoncus
        libero vel nunc consequat, quis tincidunt nisl eleifend. Cras bibendum enim a justo luctus
        vestibulum. Fusce dictum libero quis erat maximus, vitae volutpat diam dignissim.
    </string>
    <string name="dummy_button">Dummy Button</string>
    <string name="dummy_content">DUMMY\nCONTENT</string>
    <string name="amount_two_decimal_places">Please enter an amount with exactly two decimal places (e.g., 5.55 or 100.11).</string>
    <string name="no_transactions">No transactions available</string>
    <string name="transaction_list_header">Unbilled Transactions</string>
    <string name="processing_text">Processing....</string>
    <string name="expected_volume_of_transactions_monthly">Expected Volume of Transactions (€ Monthly)</string>
    <string name="expected_active_terminals_monthly">Expected Active Terminals (Monthly)</string>
    <string name="phone_number">Phone Number</string>
    <string name="enter_your_phone_number">Enter your phone number</string>
    <string name="i_agree_to_the">I agree to the</string>
    <string name="terms_of_use">Terms of Use</string>
    <string name="submit_request">Submit Request</string>
    <string name="enter_your_company_name">Enter your company name</string>
    <string name="company_name">Company Name</string>
    <string name="country">Country</string>
    <string name="merchant_type">Merchant Type</string>
    <string name="enter_your_email">Enter your email</string>
    <string name="email_address">Email Address</string>
    <string name="back_button_onboarding">Back Button</string>
    <string name="summary">Summary</string>
    <string name="enter_summary">Enter Title</string>
    <string name="description">Description</string>
    <string name="enter_description">Enter Description</string>
    <string name="issue_type">Issue Type</string>
    <string name="allcheckswithterms">Please fill all required fields, select valid options, and agree to terms and conditions</string>
    <string name="allchecks">Please fill all required fields, select valid options</string>
    <string name="no_internet_connection">No Internet Connection</string>
    <string name="internet_setting_msg">Please check your internet settings.</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="payment_success_icon">Payment Success Icon</string>
    <string name="payment_success_title">Payment Success!</string>
    <string name="payment_success_subtitle">Your payment has been successfully done.</string>
    <string name="label_amount">Amount</string>
    <string name="dummy_amount">EUR 55.22</string>
    <string name="label_payment_status">Payment Status</string>
    <string name="dummy_approved">Approved</string>
    <string name="label_ref_number">Ref Number</string>
    <string name="ref_number_value">221122</string>
    <string name="label_merchant_name">Merchant Name</string>
    <string name="merchant_name_value">Otomoto Car</string>
    <string name="label_payment_method">Payment Method</string>
    <string name="label_date">Date</string>
    <string name="date_value">Mar 22, 2023, 13:22:16</string>
    <string name="label_card_detail">Card Details</string>
    <string name="label_transaction_type">Transaction Type</string>
    <string name="transaction_type_value">Purchase</string>
    <string name="label_tvr">TVR</string>
    <string name="tvr_value">002192838102</string>
    <string name="label_receipt_number">Receipt Number</string>
    <string name="receipt_number_value">68831771</string>
    <string name="label_rrn">RRN</string>
    <string name="rrn_value">213213213213</string>
    <string name="label_auth_code">Auth Code</string>
    <string name="auth_code_value">21321321</string>
    <string name="label_aid">AID</string>
    <string name="aid_value">123123123123123123</string>
    <string name="btn_share_via_email">Email Receipt</string>
    <string name="btn_download_receipt">Download Receipt</string>
    <string name="btn_done">Back to Home</string>
    <string name="view_transaction_details">View Transaction Details</string>
    <string name="lets_get_started">Let’s Get Started</string>
    <string name="seamless_transactions_one_smart_solution">Seamless Transactions, One Smart Solution</string>
    <string name="txt_continue">Continue</string>
    <string name="welcome_back">Welcome Back!</string>
    <string name="enter_your_username_password"><![CDATA[Enter Your Password & Password]]></string>
    <string name="example_domain_com"><EMAIL></string>
    <string name="password">Password</string>
    <string name="fingerprint_icon">fingerprint icon</string>
    <string name="partner_with_us_as_a_merchant">
    Partner with Us as a <font color="#617C58"><u>Merchant</u></font>
</string>
    <string name="toggle_password_visibility">Toggle Password</string>
    <string name="merchant_onboarding">Merchant Onboarding</string>
    <string name="send">Send</string>
    <string name="sdk_this_will_only_take_a_moment">This will only take a moment</string>
    <string name="terminal_status">Terminal Status</string>
    <string name="close">Close</string>
    <string name="choose_vat">Choose VAT %</string>

    <string name="select_vat">Select VAT</string>
    <string name="vat_off">Off</string>
    <string name="vat_6">6%</string>
    <string name="vat_12">12%</string>
    <string name="vat_25">25%</string>
    <string name="apply">Apply VAT</string>
    <string name="vat_amount">VAT Amount</string>
    <string name="report_a_issue">Bug Alert</string>
    <string name="copy_icon">Copy</string>
    <string name="open_settings">Open Settings</string>
    <string name="exit">Exit</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="are_you_sure_you_want_to_perform_this_action">Are you sure you want to perform this action?</string>
    <string name="reverse">REVERSE</string>
    <string name="unbind_terminal_description">If you unbind the payment terminal, all unsettled transactions will be settled, and the business day will close. This action cannot be undone. Do you wish to proceed?</string>
    <string name="unbind_payment_terminal">Unbind Payment Terminal</string>
    <string name="confirm_payment">Confirm Payment</string>
    <string name="fivepercent">5%</string>
    <string name="add_a_tip">Add a Tip</string>
    <string name="tenpercent">10%</string>
    <string name="fifteenpercent">15%</string>
    <string name="twentypercent">20%</string>
    <string name="zeropercent">0%</string>
    <string name="terminal_settings">Terminal Settings</string>
    <string name="activate_tip_feature">Activate Tip Feature</string>
    <string name="enter_name">Enter name</string>
    <string name="tip_amount">Tip Amount</string>
    <string name="tip">Tip %</string>
    <string name="print_receipt">Print Receipt</string>

</resources>