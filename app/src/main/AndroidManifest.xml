<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.HIDE_OVERLAY_WINDOWS" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <uses-feature
        android:name="android.hardware.nfc"
        android:required="true" />

    <uses-permission android:name="android.permission.USE_BIOMETRIC" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:enableOnBackInvokedCallback="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_hazelpay_merchant"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_hazelpay_merchant_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.EftaaPay"
        android:usesCleartextTraffic="true"
        tools:replace="android:usesCleartextTraffic"
        tools:targetApi="tiramisu">
        <activity
            android:name=".ConfirmationScreen"
            android:exported="false" />
        <activity
            android:name=".SupportScreen"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".OnboardingForm"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ChangePasswordScreen"
            android:exported="false" />
        <activity
            android:name=".ChangePinScreen"
            android:exported="false" />
        <activity
            android:name=".SplashScreen"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ReportsScreen"
            android:exported="false" />
        <activity
            android:name=".PaymentScreen"
            android:exported="false" />
        <activity
            android:name=".AddEmailScreen"
            android:exported="false" />
        <activity
            android:name=".PaymentReceiptScreen"
            android:exported="false" />
        <activity
            android:name=".SignatureScreen"
            android:exported="false" />
        <activity
            android:name=".TransferFailedScreen"
            android:exported="false" />
        <activity
            android:name=".TransferSuccessfulScreen"
            android:exported="false" />
        <activity
            android:name=".Tap2PayScreen"
            android:exported="false" />
        <activity
            android:name=".HomeScreen"
            android:exported="false" />
        <activity
            android:name=".SettingsScreen"
            android:exported="false" />
        <activity
            android:name=".PinCode"
            android:exported="false" />
        <activity
            android:name=".SecurityQuestions"
            android:exported="false" />
        <activity
            android:name=".AddPhoneScreen"
            android:exported="false" />
        <activity
            android:name=".AddCustomerPhoneScreen"
            android:exported="false" />
        <activity
            android:name=".LoginScreen"
            android:exported="false" />
        <activity
            android:name=".ProfileUpdate"
            android:exported="false" />
        <activity
            android:name=".SignupScreen"
            android:exported="false" />
        <activity
            android:name=".CardPinCode"
            android:exported="false" />
        <activity
            android:name=".OnboardingScreen"
            android:exported="false"
            android:theme="@style/Theme.EftaaPay" />
    </application>

</manifest>